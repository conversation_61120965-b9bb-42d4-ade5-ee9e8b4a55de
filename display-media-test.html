<!DOCTYPE html>
<html>
<head>
    <title>Тест getDisplayMedia для аудио</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖥️ Тест getDisplayMedia для аудио</h1>
        
        <div class="test-section">
            <h3>1. Проверка поддержки API</h3>
            <button onclick="checkSupport()">Проверить поддержку</button>
            <div id="supportResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Тест захвата аудио экрана</h3>
            <button onclick="testScreenAudio()" id="screenBtn">Захватить аудио экрана</button>
            <button onclick="stopCapture()" id="stopBtn" disabled>Остановить</button>
            <div id="screenResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Тест захвата аудио вкладки</h3>
            <button onclick="testTabAudio()" id="tabBtn">Захватить аудио вкладки</button>
            <div id="tabResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Тест записи аудио</h3>
            <button onclick="testRecording()" id="recordBtn">Тест записи (5 сек)</button>
            <div id="recordResult" class="result" style="display: none;"></div>
        </div>
    </div>
    
    <script>
        let currentStream = null;
        let mediaRecorder = null;
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }
        
        function checkSupport() {
            let result = 'Проверка поддержки API:\n\n';
            
            result += `navigator.mediaDevices: ${navigator.mediaDevices ? '✅ Поддерживается' : '❌ Не поддерживается'}\n`;
            result += `getDisplayMedia: ${navigator.mediaDevices?.getDisplayMedia ? '✅ Поддерживается' : '❌ Не поддерживается'}\n`;
            result += `getUserMedia: ${navigator.mediaDevices?.getUserMedia ? '✅ Поддерживается' : '❌ Не поддерживается'}\n`;
            result += `MediaRecorder: ${window.MediaRecorder ? '✅ Поддерживается' : '❌ Не поддерживается'}\n\n`;
            
            result += `Браузер: ${navigator.userAgent}\n`;
            result += `Протокол: ${location.protocol}\n`;
            result += `Хост: ${location.host}`;
            
            const type = navigator.mediaDevices?.getDisplayMedia ? 'success' : 'error';
            showResult('supportResult', result, type);
        }
        
        async function testScreenAudio() {
            try {
                showResult('screenResult', 'Запрос разрешения на захват экрана...', 'info');
                
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: true,
                    audio: true
                });
                
                currentStream = stream;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('screenBtn').disabled = true;
                
                const videoTracks = stream.getVideoTracks();
                const audioTracks = stream.getAudioTracks();
                
                let result = '✅ Захват экрана успешен!\n\n';
                result += `Видео треки: ${videoTracks.length}\n`;
                result += `Аудио треки: ${audioTracks.length}\n\n`;
                
                if (videoTracks.length > 0) {
                    const videoTrack = videoTracks[0];
                    const settings = videoTrack.getSettings();
                    result += `Видео: ${settings.width}x${settings.height}\n`;
                    result += `Источник: ${settings.displaySurface || 'неизвестно'}\n\n`;
                }
                
                if (audioTracks.length > 0) {
                    const audioTrack = audioTracks[0];
                    const settings = audioTrack.getSettings();
                    result += `Аудио: ${audioTrack.label}\n`;
                    result += `Sample Rate: ${settings.sampleRate}Hz\n`;
                    result += `Channel Count: ${settings.channelCount}\n`;
                } else {
                    result += '⚠️ Аудио треки не найдены\n';
                    result += 'Возможно, источник не поддерживает аудио';
                }
                
                showResult('screenResult', result, audioTracks.length > 0 ? 'success' : 'error');
                
            } catch (error) {
                showResult('screenResult', `❌ Ошибка: ${error.message}`, 'error');
                resetButtons();
            }
        }
        
        async function testTabAudio() {
            try {
                showResult('tabResult', 'Запрос разрешения на захват вкладки...', 'info');
                
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'tab'
                    },
                    audio: {
                        echoCancellation: false,
                        noiseSuppression: false,
                        autoGainControl: false
                    }
                });
                
                const videoTracks = stream.getVideoTracks();
                const audioTracks = stream.getAudioTracks();
                
                let result = '✅ Захват вкладки успешен!\n\n';
                result += `Видео треки: ${videoTracks.length}\n`;
                result += `Аудио треки: ${audioTracks.length}\n\n`;
                
                if (audioTracks.length > 0) {
                    const audioTrack = audioTracks[0];
                    const settings = audioTrack.getSettings();
                    result += `Аудио: ${audioTrack.label}\n`;
                    result += `Sample Rate: ${settings.sampleRate}Hz\n`;
                    result += `Channel Count: ${settings.channelCount}\n`;
                    
                    // Создаем только аудио поток
                    const audioStream = new MediaStream();
                    audioTracks.forEach(track => audioStream.addTrack(track));
                    
                    result += '\n✅ Аудио поток создан для записи';
                } else {
                    result += '⚠️ Аудио треки не найдены';
                }
                
                // Останавливаем поток
                stream.getTracks().forEach(track => track.stop());
                
                showResult('tabResult', result, audioTracks.length > 0 ? 'success' : 'error');
                
            } catch (error) {
                showResult('tabResult', `❌ Ошибка: ${error.message}`, 'error');
            }
        }
        
        async function testRecording() {
            try {
                showResult('recordResult', 'Запуск записи...', 'info');
                
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'tab',
                        width: { ideal: 1 },
                        height: { ideal: 1 }
                    },
                    audio: {
                        echoCancellation: false,
                        noiseSuppression: false,
                        autoGainControl: false,
                        sampleRate: 48000
                    }
                });
                
                // Создаем только аудио поток
                const audioStream = new MediaStream();
                stream.getAudioTracks().forEach(track => {
                    audioStream.addTrack(track);
                });
                
                // Останавливаем видео
                stream.getVideoTracks().forEach(track => track.stop());
                
                if (audioStream.getAudioTracks().length === 0) {
                    throw new Error('Нет аудио треков для записи');
                }
                
                // Настраиваем MediaRecorder
                let mimeType = 'audio/webm;codecs=opus';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'audio/webm';
                }
                
                mediaRecorder = new MediaRecorder(audioStream, {
                    mimeType: mimeType,
                    audioBitsPerSecond: 128000
                });
                
                const chunks = [];
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        chunks.push(event.data);
                    }
                };
                
                mediaRecorder.onstop = () => {
                    const blob = new Blob(chunks, { type: mimeType });
                    const url = URL.createObjectURL(blob);
                    
                    let result = '✅ Запись завершена!\n\n';
                    result += `Размер файла: ${(blob.size / 1024).toFixed(2)} KB\n`;
                    result += `Формат: ${mimeType}\n`;
                    result += `Длительность: 5 секунд\n\n`;
                    result += 'Файл готов к скачиванию';
                    
                    showResult('recordResult', result, 'success');
                    
                    // Создаем ссылку для скачивания
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `test-recording-${Date.now()}.webm`;
                    a.click();
                    
                    setTimeout(() => URL.revokeObjectURL(url), 1000);
                    audioStream.getTracks().forEach(track => track.stop());
                };
                
                mediaRecorder.start(1000);
                showResult('recordResult', 'Запись... (5 секунд)', 'info');
                
                setTimeout(() => {
                    if (mediaRecorder && mediaRecorder.state === 'recording') {
                        mediaRecorder.stop();
                    }
                }, 5000);
                
            } catch (error) {
                showResult('recordResult', `❌ Ошибка записи: ${error.message}`, 'error');
            }
        }
        
        function stopCapture() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
            }
            resetButtons();
            showResult('screenResult', 'Захват остановлен', 'info');
        }
        
        function resetButtons() {
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('screenBtn').disabled = false;
        }
    </script>
</body>
</html>
