# 📁 Где найти записанные файлы?

## 🎯 Что сохраняется после записи

После остановки записи автоматически сохраняются **2 файла**:

### 1. 🎵 Аудио файл
- **Формат**: `.webm` (аудио)
- **Название**: `Meeting_YYYY-MM-DD_HH-MM-SS.webm`
- **Содержимое**: Запись звука Google Meet встречи

### 2. 📝 Транскрипт
- **Формат**: `.txt` (текст)
- **Название**: `Meeting_YYYY-MM-DD_HH-MM-SS_transcript.txt`
- **Содержимое**: Текстовая расшифровка речи

## 📂 Где искать файлы

### В Windows:
```
C:\Users\<USER>\Downloads\
```

### В macOS:
```
/Users/<USER>/Downloads/
```

### В Linux:
```
/home/<USER>/Downloads/
```

## 🔍 Как найти файлы

### Способ 1: Через браузер
1. Нажмите `Ctrl+J` (Windows/Linux) или `Cmd+Shift+J` (macOS)
2. Откроется страница загрузок
3. Найдите файлы по времени загрузки

### Способ 2: Через проводник
1. Откройте папку "Загрузки" / "Downloads"
2. Отсортируйте по дате изменения
3. Найдите файлы с префиксом `Meeting_`

### Способ 3: Поиск по имени
Ищите файлы по шаблону:
- `Meeting_*` - все файлы записей
- `*_transcript.txt` - только транскрипты
- `*.webm` - только аудио файлы

## 📋 Пример файлов

После записи встречи 23 июня 2024 в 14:30 вы получите:

```
Meeting_2024-06-23_14-30-15.webm          (аудио, ~5MB)
Meeting_2024-06-23_14-30-15_transcript.txt (текст, ~2KB)
```

## 📄 Содержимое транскрипта

### Если речь была распознана:
```
Транскрипция записи Google Meet
Дата: 23.06.2024, 14:30:15
Длительность: 00:15:30
Языки: Русский и Английский (автоматическое переключение)

--- ТРАНСКРИПТ ---

Привет всем! Как дела? Hello everyone, how are you?
Сегодня мы обсуждаем новый проект...

--- КОНЕЦ ТРАНСКРИПТА ---
```

### Если речь НЕ была распознана:
```
Транскрипция записи Google Meet
Дата: 23.06.2024, 14:30:15
Длительность: 00:15:30

--- ТРАНСКРИПТ ---

(Речь не была распознана)

Возможные причины:
- Не было речи во время записи
- Проблемы с микрофоном
- Проблемы с интернет соединением
- Speech Recognition API не работал

Проверьте аудио файл для подтверждения содержимого.
```

## ❓ Часто задаваемые вопросы

### Q: Почему нет транскрипта?
**A:** Транскрипт сохраняется ВСЕГДА, даже если пустой. Проверьте:
- Папку Downloads
- Файл с суффиксом `_transcript.txt`
- Возможно, речь не была распознана

### Q: Почему транскрипт пустой?
**A:** Возможные причины:
- Во время записи никто не говорил
- Проблемы с микрофоном
- Плохое интернет соединение
- Говорили на неподдерживаемом языке

### Q: Можно ли изменить папку сохранения?
**A:** Да, в настройках Chrome:
1. `chrome://settings/downloads`
2. Измените папку загрузок
3. Все файлы будут сохраняться туда

### Q: Почему файлы не открываются?
**A:** 
- **Аудио**: Используйте VLC, Windows Media Player, или любой аудиоплеер
- **Транскрипт**: Откройте в Блокноте, TextEdit, или любом текстовом редакторе

## 💡 Полезные советы

1. **Переименовывайте файлы** сразу после записи для удобства
2. **Создайте отдельную папку** для записей встреч
3. **Проверяйте транскрипт** сразу после записи
4. **Сохраняйте важные моменты** отдельно
5. **Делайте резервные копии** важных записей

## 🔧 Если файлы не найдены

1. **Проверьте консоль браузера** (F12 → Console) на ошибки
2. **Попробуйте другую папку** - возможно, изменены настройки
3. **Перезапустите браузер** и попробуйте снова
4. **Проверьте разрешения** на запись в папку Downloads

## 📞 Поддержка

Если файлы все еще не найдены:
1. Откройте консоль разработчика (F12)
2. Найдите сообщения о сохранении файлов
3. Проверьте, есть ли ошибки красного цвета
4. Сообщите о проблеме с подробным описанием
