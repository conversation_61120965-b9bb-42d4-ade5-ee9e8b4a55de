# 🔧 Устранение проблем с транскрибацией

## Проблема: "Речь не распознана" сразу после запуска

### ✅ Это нормально!
Сообщение "Ожидание речи..." или "Речь не распознана" в первые секунды записи - это нормальное поведение. Speech Recognition API нужно время для инициализации.

### 🎯 Что делать:
1. **Подождите 2-3 секунды** после начала записи
2. **Начните говорить** четко и громко
3. **Не паникуйте** если сразу показывается "Ожидание речи"

## Основные проблемы и решения

### 🎤 Проблемы с микрофоном

**Симптомы:**
- "Ошибка: проблема с микрофоном"
- "Нет доступа к микрофону"

**Решения:**
1. Разрешите доступ к микрофону в браузере
2. Проверьте, что микрофон работает в других приложениях
3. Перезагрузите страницу Google Meet
4. Попробуйте другой браузер (рекомендуется Chrome)

### 🌐 Проблемы с интернетом

**Симптомы:**
- Транскрибация работает с задержкой
- Частые переключения языков
- "Ошибка сети"

**Решения:**
1. Проверьте стабильность интернет соединения
2. Закройте другие вкладки с видео/аудио
3. Попробуйте перезапустить запись

### 🗣️ Проблемы с распознаванием речи

**Симптомы:**
- Текст не появляется при разговоре
- Неправильное распознавание слов
- Смешивание языков

**Решения:**
1. **Говорите четче и громче**
2. **Используйте качественный микрофон** (особенно для Bluetooth)
3. **Говорите ближе к микрофону**
4. **Избегайте фонового шума**
5. **Говорите на одном языке** (русский или английский)

### 🔄 Автоматическое переключение языков

Расширение автоматически переключается между русским и английским языками при проблемах с распознаванием.

**Индикаторы:**
- "Переключение на английский..."
- "Переключение на русский..."
- "Слушаю... (English)" или "Слушаю... (Русский)"

**Это нормально!** Система пытается найти лучший язык для распознавания.

## 🧪 Тестирование

### Перед использованием расширения:

1. **Откройте `mic-test.html`**
2. **Протестируйте микрофон**
3. **Протестируйте Speech Recognition на русском**
4. **Протестируйте Speech Recognition на английском**

### Если тесты не проходят:
- Проблема не в расширении, а в настройках браузера/системы
- Проверьте настройки приватности Chrome
- Убедитесь, что используете HTTPS (Google Meet всегда HTTPS)

## 📱 Рекомендации для лучшего качества

### Для обычного микрофона:
- Расстояние до микрофона: 15-30 см
- Говорите прямо в микрофон
- Избегайте эха (закройте окна, используйте мягкую мебель)

### Для Bluetooth наушников (AirPods и др.):
- Убедитесь в стабильном соединении
- Зарядите наушники
- Говорите четко (Bluetooth микрофоны менее чувствительны)
- При проблемах переподключите Bluetooth

### Для встреч:
- Отключите микрофон в Meet когда не говорите
- Используйте наушники чтобы избежать эха
- Предупредите участников о записи

## 🚨 Критические ошибки

### "Доступ запрещен"
1. Перейдите в настройки Chrome: `chrome://settings/content/microphone`
2. Убедитесь, что `meet.google.com` разрешен
3. Перезагрузите страницу

### "API не поддерживается"
1. Обновите Chrome до последней версии
2. Убедитесь, что используете Chrome (не Safari/Firefox)
3. Проверьте, что включен JavaScript

### Расширение не работает
1. Перезагрузите расширение в `chrome://extensions/`
2. Проверьте, что все файлы на месте
3. Посмотрите консоль разработчика (F12) на ошибки

## 💡 Полезные советы

1. **Начинайте говорить через 3-5 секунд** после запуска записи
2. **Делайте паузы** между предложениями для лучшего распознавания
3. **Используйте простые слова** для важных моментов
4. **Проверяйте транскрипт** во время записи в popup
5. **Сохраняйте важные моменты** отдельно, не полагайтесь только на автоматическую транскрибацию

## 🔍 Отладка

Если проблемы продолжаются:
1. Откройте консоль разработчика (F12)
2. Перейдите на вкладку Console
3. Начните запись и посмотрите на сообщения
4. Ищите ошибки красного цвета
5. Сообщите о проблеме с подробным описанием ошибок
