class MeetRecorderContent {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.startTime = null;
        this.audioContext = null;
        this.mixedStream = null;
        this.recognition = null;
        this.transcript = '';
        this.isTranscribing = false;

        this.initEventListeners();
        this.injectRecorderScript();
        this.initSpeechRecognition();
    }

    initSpeechRecognition() {
        try {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (!SpeechRecognition) {
                console.warn('Speech Recognition API не поддерживается');
                return;
            }

            this.recognition = new SpeechRecognition();
            this.recognition.continuous = true;
            this.recognition.interimResults = true;
            this.recognition.maxAlternatives = 1;

            // Начинаем с русского языка
            this.recognition.lang = 'ru-RU';
            this.currentLang = 'ru-RU';
            this.langSwitchCount = 0;

            this.recognition.onstart = () => {
                console.log(`Транскрибация началась (${this.currentLang})`);
                this.isTranscribing = true;

                // Уведомляем popup о начале транскрибации
                chrome.runtime.sendMessage({
                    action: 'transcriptUpdate',
                    transcript: this.transcript,
                    interim: `Слушаю... (${this.currentLang === 'ru-RU' ? 'Русский' : 'English'})`
                });
            };

            this.recognition.onresult = (event) => {
                let interimTranscript = '';
                let finalTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    const confidence = event.results[i][0].confidence || 0;

                    if (event.results[i].isFinal) {
                        finalTranscript += transcript + ' ';
                        console.log(`Распознано (${this.currentLang}): "${transcript}" (уверенность: ${confidence.toFixed(2)})`);
                    } else {
                        interimTranscript += transcript;
                    }
                }

                if (finalTranscript) {
                    this.transcript += finalTranscript;
                    this.saveTranscriptChunk(finalTranscript);
                }

                // Отправляем обновление в popup
                chrome.runtime.sendMessage({
                    action: 'transcriptUpdate',
                    transcript: this.transcript,
                    interim: interimTranscript
                });
            };

            this.recognition.onerror = (event) => {
                console.log(`Событие Speech Recognition (${this.currentLang}):`, event.error);

                switch(event.error) {
                    case 'no-speech':
                        // Это нормально - просто нет речи, продолжаем слушать
                        console.log('Речь не обнаружена, ожидаем...');
                        chrome.runtime.sendMessage({
                            action: 'transcriptUpdate',
                            transcript: this.transcript,
                            interim: 'Ожидание речи...'
                        });
                        // Не останавливаем, просто продолжаем
                        return;

                    case 'audio-capture':
                        console.error('Проблема с захватом аудио');
                        chrome.runtime.sendMessage({
                            action: 'transcriptUpdate',
                            transcript: this.transcript,
                            interim: 'Ошибка: проблема с микрофоном'
                        });
                        break;

                    case 'not-allowed':
                        console.error('Доступ к микрофону запрещен');
                        chrome.runtime.sendMessage({
                            action: 'transcriptUpdate',
                            transcript: this.transcript,
                            interim: 'Ошибка: нет доступа к микрофону'
                        });
                        break;

                    case 'language-not-supported':
                    case 'network':
                        // Пробуем переключиться на другой язык
                        if (this.langSwitchCount < 2) {
                            this.langSwitchCount++;
                            const newLang = this.currentLang === 'ru-RU' ? 'en-US' : 'ru-RU';
                            console.log(`Переключаемся на ${newLang}`);
                            this.currentLang = newLang;
                            this.recognition.lang = newLang;

                            chrome.runtime.sendMessage({
                                action: 'transcriptUpdate',
                                transcript: this.transcript,
                                interim: `Переключение на ${newLang === 'ru-RU' ? 'русский' : 'английский'}...`
                            });

                            if (this.isRecording) {
                                setTimeout(() => {
                                    try {
                                        this.recognition.start();
                                    } catch (startError) {
                                        console.error('Не удалось перезапустить с новым языком:', startError);
                                    }
                                }, 1000);
                            }
                        }
                        break;

                    default:
                        console.warn(`Неизвестная ошибка Speech Recognition: ${event.error}`);
                        break;
                }
            };

            this.recognition.onend = () => {
                console.log('Speech Recognition остановлен');
                this.isTranscribing = false;

                // Перезапускаем если запись еще идет
                if (this.isRecording) {
                    console.log('Перезапуск транскрибации...');
                    setTimeout(() => {
                        try {
                            if (this.isRecording) { // Проверяем еще раз
                                this.recognition.start();
                                chrome.runtime.sendMessage({
                                    action: 'transcriptUpdate',
                                    transcript: this.transcript,
                                    interim: `Слушаю... (${this.currentLang === 'ru-RU' ? 'Русский' : 'English'})`
                                });
                            }
                        } catch (error) {
                            console.warn('Не удалось перезапустить транскрибацию:', error);
                            chrome.runtime.sendMessage({
                                action: 'transcriptUpdate',
                                transcript: this.transcript,
                                interim: 'Ошибка перезапуска транскрибации'
                            });
                        }
                    }, 500); // Уменьшили задержку
                }
            };

        } catch (error) {
            console.error('Ошибка инициализации Speech Recognition:', error);
        }
    }
    
    initEventListeners() {
        // Слушаем сообщения от popup и background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
        
        // Отслеживаем изменения в DOM для детекции начала/окончания встречи
        this.observeMeetChanges();
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'startRecording':
                    await this.startRecording(sendResponse);
                    break;
                    
                case 'stopRecording':
                    await this.stopRecording(sendResponse);
                    break;
                    
                case 'getRecordingStatus':
                    sendResponse({
                        isRecording: this.isRecording,
                        startTime: this.startTime
                    });
                    break;
                    
                case 'forceStopRecording':
                    console.log('Получена команда принудительной остановки');
                    await this.forceStopRecording();
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async startRecording(sendResponse) {
        try {
            if (this.isRecording) {
                sendResponse({ success: false, error: 'Запись уже идет' });
                return;
            }

            // Используем getDisplayMedia для захвата аудио
            const displayStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    mediaSource: 'tab',
                    width: { ideal: 1 },
                    height: { ideal: 1 }
                },
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false,
                    sampleRate: 48000
                }
            });

            // Извлекаем только аудио треки
            const tabStream = new MediaStream();
            displayStream.getAudioTracks().forEach(track => {
                tabStream.addTrack(track);
                console.log('Аудио трек добавлен:', track.label);
            });

            // Останавливаем видео треки
            displayStream.getVideoTracks().forEach(track => {
                track.stop();
                console.log('Видео трек остановлен');
            });
            
            // Получаем поток с микрофона с оптимизацией для Bluetooth устройств
            let micStream = null;
            try {
                // Сначала получаем список доступных устройств
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');

                console.log('Доступные аудио устройства:', audioInputs);

                // Настройки для лучшего качества с Bluetooth устройствами
                const audioConstraints = {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 48000,
                    channelCount: 2,
                    latency: 0.01, // Низкая задержка
                    volume: 1.0
                };

                // Пробуем использовать предпочтительное устройство (если есть Bluetooth)
                const bluetoothDevice = audioInputs.find(device =>
                    device.label.toLowerCase().includes('airpods') ||
                    device.label.toLowerCase().includes('bluetooth') ||
                    device.label.toLowerCase().includes('wireless')
                );

                if (bluetoothDevice && bluetoothDevice.deviceId) {
                    console.log('Используем Bluetooth устройство:', bluetoothDevice.label);
                    audioConstraints.deviceId = { exact: bluetoothDevice.deviceId };
                }

                micStream = await navigator.mediaDevices.getUserMedia({
                    audio: audioConstraints
                });

                console.log('Микрофон подключен:', micStream.getAudioTracks()[0].label);

            } catch (micError) {
                console.warn('Не удалось получить доступ к микрофону:', micError);

                // Пробуем с базовыми настройками
                try {
                    micStream = await navigator.mediaDevices.getUserMedia({
                        audio: true
                    });
                    console.log('Микрофон подключен с базовыми настройками');
                } catch (fallbackError) {
                    console.error('Полностью не удалось подключить микрофон:', fallbackError);
                }
            }
            
            // Смешиваем аудио потоки (только аудио)
            this.mixedStream = await this.mixAudioStreams(tabStream, micStream);

            // Настраиваем MediaRecorder только для аудио
            this.setupMediaRecorder(this.mixedStream);

            // Начинаем запись
            this.mediaRecorder.start(1000); // Сохраняем данные каждую секунду
            this.isRecording = true;
            this.startTime = Date.now();
            this.recordedChunks = [];
            this.transcript = '';

            // Запускаем транскрибацию с задержкой
            if (this.recognition) {
                setTimeout(() => {
                    try {
                        console.log('Запуск транскрибации...');
                        this.recognition.start();
                    } catch (error) {
                        console.warn('Не удалось запустить транскрибацию:', error);
                    }
                }, 2000); // Задержка 2 секунды
            }

            // Уведомляем background script
            chrome.runtime.sendMessage({
                action: 'recordingStarted',
                startTime: this.startTime
            });

            sendResponse({ success: true });
            
        } catch (error) {
            console.error('Error starting recording:', error);
            this.notifyError(error.message);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async mixAudioStreams(tabStream, micStream) {
        // Создаем AudioContext для смешивания аудио потоков
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Настраиваем AudioContext для лучшего качества
        if (this.audioContext.sampleRate !== 48000) {
            console.log(`AudioContext sample rate: ${this.audioContext.sampleRate}`);
        }

        const destination = this.audioContext.createMediaStreamDestination();

        // Добавляем аудио с вкладки (звук Meet)
        if (tabStream.getAudioTracks().length > 0) {
            const tabAudio = this.audioContext.createMediaStreamSource(tabStream);
            const tabGain = this.audioContext.createGain();
            tabGain.gain.value = 0.9; // Немного уменьшаем звук вкладки

            tabAudio.connect(tabGain);
            tabGain.connect(destination);

            console.log('Аудио вкладки подключено');
        }

        // Добавляем аудио с микрофона с обработкой для Bluetooth
        if (micStream && micStream.getAudioTracks().length > 0) {
            const micTrack = micStream.getAudioTracks()[0];
            console.log('Настройки микрофона:', micTrack.getSettings());

            const micAudio = this.audioContext.createMediaStreamSource(micStream);
            const micGain = this.audioContext.createGain();
            micGain.gain.value = 1.2; // Усиливаем микрофон

            // Добавляем компрессор для стабилизации уровня
            const compressor = this.audioContext.createDynamicsCompressor();
            compressor.threshold.value = -24;
            compressor.knee.value = 30;
            compressor.ratio.value = 12;
            compressor.attack.value = 0.003;
            compressor.release.value = 0.25;

            micAudio.connect(micGain);
            micGain.connect(compressor);
            compressor.connect(destination);

            console.log('Микрофон подключен с обработкой');
        }

        return destination.stream;
    }
    
    setupMediaRecorder(stream) {
        // Определяем поддерживаемый аудио формат
        let mimeType = 'audio/webm;codecs=opus';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'audio/webm';
        }
        if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'audio/mp4';
        }

        this.mediaRecorder = new MediaRecorder(stream, {
            mimeType: mimeType,
            audioBitsPerSecond: 128000
        });
        
        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                this.recordedChunks.push(event.data);
            }
        };
        
        this.mediaRecorder.onstop = () => {
            this.saveRecording();
        };
        
        this.mediaRecorder.onerror = (event) => {
            console.error('MediaRecorder error:', event.error);
            this.notifyError('Ошибка записи: ' + event.error.message);
        };
    }
    
    async stopRecording(sendResponse) {
        try {
            if (!this.isRecording) {
                sendResponse({ success: false, error: 'Запись не идет' });
                return;
            }
            
            await this.forceStopRecording();
            sendResponse({ success: true });
            
        } catch (error) {
            console.error('Error stopping recording:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async forceStopRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
            this.mediaRecorder.stop();
        }

        // Останавливаем транскрибацию
        if (this.recognition && this.isTranscribing) {
            try {
                this.recognition.stop();
            } catch (error) {
                console.warn('Ошибка остановки транскрибации:', error);
            }
        }

        // Останавливаем все треки
        if (this.mixedStream) {
            this.mixedStream.getTracks().forEach(track => track.stop());
        }

        // Закрываем AudioContext
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }

        this.isRecording = false;
        this.isTranscribing = false;
        this.startTime = null;
        this.mixedStream = null;

        // Уведомляем background script
        chrome.runtime.sendMessage({ action: 'recordingStopped' });
    }

    saveTranscriptChunk(text) {
        const timestamp = new Date().toLocaleTimeString('ru-RU');
        const chunk = `[${timestamp}] ${text.trim()}\n`;

        // Сохраняем в локальное хранилище
        chrome.storage.local.get('currentTranscript').then(result => {
            const currentTranscript = result.currentTranscript || '';
            chrome.storage.local.set({
                currentTranscript: currentTranscript + chunk
            });
        });
    }

    saveTranscriptFile(filename, transcript) {
        const blob = new Blob([transcript], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        setTimeout(() => URL.revokeObjectURL(url), 1000);

        console.log(`Транскрипт сохранен: ${filename}`);
    }

    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        const displaySeconds = seconds % 60;
        const displayMinutes = minutes % 60;

        return `${hours.toString().padStart(2, '0')}:${displayMinutes.toString().padStart(2, '0')}:${displaySeconds.toString().padStart(2, '0')}`;
    }
    
    async saveRecording() {
        if (this.recordedChunks.length === 0) {
            console.warn('Нет данных для сохранения');
            return;
        }

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);

        // Получаем информацию о встрече для имени файла
        const meetInfo = await this.getMeetingInfo();

        // Создаем детальное имя файла
        const now = new Date();
        const dateStr = now.toLocaleDateString('ru-RU').replace(/\./g, '-');
        const timeStr = now.toLocaleTimeString('ru-RU', { hour12: false }).replace(/:/g, '-');

        let filename = `Google-Meet-${dateStr}_${timeStr}`;

        // Добавляем название встречи если доступно
        if (meetInfo.title) {
            const cleanTitle = meetInfo.title.replace(/[^\w\s-]/g, '').replace(/\s+/g, '-').substring(0, 50);
            filename = `${cleanTitle}_${filename}`;
        }

        filename += '.webm';

        // Также сохраняем транскрипт
        console.log('Проверка транскрипта для сохранения:', {
            transcriptLength: this.transcript.length,
            transcriptContent: this.transcript.substring(0, 100) + '...',
            hasContent: this.transcript.trim().length > 0
        });

        if (this.transcript.trim()) {
            const transcriptFilename = filename.replace('.webm', '_transcript.txt');
            console.log('Сохраняем транскрипт:', transcriptFilename);

            // Добавляем заголовок к транскрипту
            const fullTranscript = `Транскрипция записи Google Meet
Дата: ${new Date().toLocaleString('ru-RU')}
Длительность: ${this.formatTime(Date.now() - this.startTime)}
Языки: Русский и Английский (автоматическое переключение)

--- ТРАНСКРИПТ ---

${this.transcript.trim()}

--- КОНЕЦ ТРАНСКРИПТА ---

Примечание: Транскрипция выполнена автоматически с помощью Web Speech API.
Возможны неточности в распознавании речи.`;

            this.saveTranscriptFile(transcriptFilename, fullTranscript);

            // Уведомляем popup о сохранении
            chrome.runtime.sendMessage({
                action: 'transcriptSaved',
                filename: transcriptFilename,
                hasContent: true,
                wordCount: this.transcript.trim().split(/\s+/).length
            });
        } else {
            // Сохраняем пустой транскрипт с информацией
            const transcriptFilename = filename.replace('.webm', '_transcript.txt');
            const emptyTranscript = `Транскрипция записи Google Meet
Дата: ${new Date().toLocaleString('ru-RU')}
Длительность: ${this.formatTime(Date.now() - this.startTime)}

--- ТРАНСКРИПТ ---

(Речь не была распознана)

Возможные причины:
- Не было речи во время записи
- Проблемы с микрофоном
- Проблемы с интернет соединением
- Speech Recognition API не работал

Проверьте аудио файл для подтверждения содержимого.`;
            console.log('Сохраняем пустой транскрипт с информацией:', transcriptFilename);
            this.saveTranscriptFile(transcriptFilename, emptyTranscript);

            // Уведомляем popup о сохранении пустого транскрипта
            chrome.runtime.sendMessage({
                action: 'transcriptSaved',
                filename: transcriptFilename,
                hasContent: false,
                wordCount: 0
            });
        }

        // Создаем ссылку для скачивания
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        // Сохраняем метаданные записи
        await this.saveRecordingMetadata({
            filename: filename,
            startTime: this.startTime,
            endTime: Date.now(),
            duration: Date.now() - this.startTime,
            meetingTitle: meetInfo.title,
            participantCount: meetInfo.participantCount,
            fileSize: blob.size
        });

        // Освобождаем URL
        setTimeout(() => URL.revokeObjectURL(url), 1000);

        console.log(`Запись сохранена: ${filename}`);

        // Показываем уведомление пользователю
        this.showSaveNotification(filename, blob.size);
    }

    async getMeetingInfo() {
        return new Promise((resolve) => {
            // Отправляем запрос в injected script для получения информации о встрече
            window.postMessage({ type: 'GET_MEET_STATE' }, '*');

            const handleResponse = (event) => {
                if (event.source === window && event.data.type === 'MEET_STATE_RESPONSE') {
                    window.removeEventListener('message', handleResponse);
                    resolve({
                        title: event.data.state.meetingTitle || '',
                        participantCount: event.data.state.participantCount || 0
                    });
                }
            };

            window.addEventListener('message', handleResponse);

            // Таймаут на случай если injected script не отвечает
            setTimeout(() => {
                window.removeEventListener('message', handleResponse);
                resolve({ title: '', participantCount: 0 });
            }, 1000);
        });
    }

    async saveRecordingMetadata(metadata) {
        try {
            // Сохраняем метаданные в chrome.storage
            const recordings = await chrome.storage.local.get('recordings') || { recordings: [] };
            recordings.recordings = recordings.recordings || [];

            recordings.recordings.push({
                id: Date.now().toString(),
                ...metadata,
                timestamp: new Date().toISOString()
            });

            // Ограничиваем количество сохраненных записей (последние 50)
            if (recordings.recordings.length > 50) {
                recordings.recordings = recordings.recordings.slice(-50);
            }

            await chrome.storage.local.set(recordings);

        } catch (error) {
            console.error('Ошибка сохранения метаданных:', error);
        }
    }

    showSaveNotification(filename, fileSize) {
        // Создаем временное уведомление
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            font-family: 'Segoe UI', sans-serif;
            font-size: 14px;
            max-width: 300px;
        `;

        const sizeInMB = (fileSize / (1024 * 1024)).toFixed(1);
        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">✅ Запись сохранена</div>
            <div style="font-size: 12px; opacity: 0.9;">
                ${filename}<br>
                Размер: ${sizeInMB} МБ
            </div>
        `;

        document.body.appendChild(notification);

        // Удаляем уведомление через 5 секунд
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    observeMeetChanges() {
        // Наблюдаем за изменениями в DOM для автоматической остановки записи
        const observer = new MutationObserver((mutations) => {
            // Проверяем, покинул ли пользователь встречу
            const meetEnded = document.querySelector('[data-call-ended="true"]') || 
                             document.querySelector('[aria-label*="покинуть встречу"]') ||
                             !document.querySelector('[data-meeting-title]');
            
            if (meetEnded && this.isRecording) {
                console.log('Встреча завершена, останавливаем запись');
                this.forceStopRecording();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true
        });
    }
    
    injectRecorderScript() {
        // Внедряем дополнительный скрипт для более глубокой интеграции
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('injected.js');
        script.onload = function() {
            this.remove();
        };
        (document.head || document.documentElement).appendChild(script);
    }
    
    notifyError(error) {
        chrome.runtime.sendMessage({
            action: 'recordingError',
            error: error
        });
    }
}

// Инициализируем content script
if (window.location.hostname === 'meet.google.com') {
    const meetRecorder = new MeetRecorderContent();
}
