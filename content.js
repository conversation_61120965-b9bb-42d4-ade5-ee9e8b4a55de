class MeetRecorderContent {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.startTime = null;
        this.audioContext = null;
        this.mixedStream = null;
        this.recognition = null;
        this.transcript = '';
        this.recordedChunks = [];
        this.audioBlob = null;

        this.initEventListeners();
        this.injectRecorderScript();
        this.injectRecordButton();
    }

    async loadTranscriptionService() {
        // Загружаем сервис транскрипции
        if (!window.TranscriptionService) {
            await this.loadScript('transcription-service.js');
        }
        this.transcriptionService = new window.TranscriptionService();
        console.log('🤖 Сервис транскрипции загружен');
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL(src);
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    initEventListeners() {
        // Слушаем сообщения от popup и background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
        
        // Отслеживаем изменения в DOM для детекции начала/окончания встречи
        this.observeMeetChanges();
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'startRecording':
                    await this.startRecording(sendResponse);
                    break;
                    
                case 'stopRecording':
                    await this.stopRecording(sendResponse);
                    break;
                    
                case 'getRecordingStatus':
                    sendResponse({
                        isRecording: this.isRecording,
                        startTime: this.startTime
                    });
                    break;
                    
                case 'forceStopRecording':
                    console.log('Получена команда принудительной остановки');
                    await this.forceStopRecording();
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async startRecording(sendResponse) {
        try {
            if (this.isRecording) {
                sendResponse({ success: false, error: 'Запись уже идет' });
                return;
            }

            // Используем getDisplayMedia для захвата аудио
            const displayStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    mediaSource: 'tab',
                    width: { ideal: 1 },
                    height: { ideal: 1 }
                },
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false,
                    sampleRate: 48000
                }
            });

            // Извлекаем только аудио треки
            const tabStream = new MediaStream();
            displayStream.getAudioTracks().forEach(track => {
                tabStream.addTrack(track);
                console.log('Аудио трек добавлен:', track.label);
            });

            // Останавливаем видео треки
            displayStream.getVideoTracks().forEach(track => {
                track.stop();
                console.log('Видео трек остановлен');
            });
            
            // Получаем поток с микрофона с оптимизацией для Bluetooth устройств
            let micStream = null;
            try {
                // Сначала получаем список доступных устройств
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');

                console.log('Доступные аудио устройства:', audioInputs);

                // Настройки для лучшего качества с Bluetooth устройствами
                const audioConstraints = {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 48000,
                    channelCount: 2,
                    latency: 0.01, // Низкая задержка
                    volume: 1.0
                };

                // Пробуем использовать предпочтительное устройство (если есть Bluetooth)
                const bluetoothDevice = audioInputs.find(device =>
                    device.label.toLowerCase().includes('airpods') ||
                    device.label.toLowerCase().includes('bluetooth') ||
                    device.label.toLowerCase().includes('wireless')
                );

                if (bluetoothDevice && bluetoothDevice.deviceId) {
                    console.log('Используем Bluetooth устройство:', bluetoothDevice.label);
                    audioConstraints.deviceId = { exact: bluetoothDevice.deviceId };
                }

                micStream = await navigator.mediaDevices.getUserMedia({
                    audio: audioConstraints
                });

                console.log('Микрофон подключен:', micStream.getAudioTracks()[0].label);

                // Тестируем Speech Recognition сразу после получения микрофона
                if (this.recognition) {
                    console.log('🧪 Тестовый запуск Speech Recognition...');
                    try {
                        this.recognition.start();
                        setTimeout(() => {
                            if (this.recognition && this.isTranscribing) {
                                this.recognition.stop();
                                console.log('🧪 Тестовый запуск завершен');
                            }
                        }, 1000);
                    } catch (testError) {
                        console.error('🧪 Тестовый запуск не удался:', testError);
                    }
                }

            } catch (micError) {
                console.warn('Не удалось получить доступ к микрофону:', micError);

                // Пробуем с базовыми настройками
                try {
                    micStream = await navigator.mediaDevices.getUserMedia({
                        audio: true
                    });
                    console.log('Микрофон подключен с базовыми настройками');
                } catch (fallbackError) {
                    console.error('Полностью не удалось подключить микрофон:', fallbackError);
                }
            }
            
            // Смешиваем аудио потоки (только аудио)
            this.mixedStream = await this.mixAudioStreams(tabStream, micStream);

            // Настраиваем MediaRecorder только для аудио
            this.setupMediaRecorder(this.mixedStream);

            // Начинаем запись
            this.mediaRecorder.start(1000); // Сохраняем данные каждую секунду
            this.isRecording = true;
            this.startTime = Date.now();
            this.recordedChunks = [];
            this.transcript = '';

            // Загружаем сервис транскрипции
            await this.loadTranscriptionService();

            // Уведомляем о начале записи
            chrome.runtime.sendMessage({
                action: 'transcriptUpdate',
                transcript: '',
                interim: '🎤 Запись началась. Транскрипция будет выполнена после остановки записи.'
            });

            // Уведомляем background script
            chrome.runtime.sendMessage({
                action: 'recordingStarted',
                startTime: this.startTime
            });

            // Обновляем UI кнопки
            this.updateRecordButtonUI();

            if (sendResponse) sendResponse({ success: true });
            
        } catch (error) {
            console.error('Error starting recording:', error);
            this.notifyError(error.message);
            sendResponse({ success: false, error: error.message });
        }
    }



    async mixAudioStreams(tabStream, micStream) {
        // Создаем AudioContext для смешивания аудио потоков
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Настраиваем AudioContext для лучшего качества
        if (this.audioContext.sampleRate !== 48000) {
            console.log(`AudioContext sample rate: ${this.audioContext.sampleRate}`);
        }

        const destination = this.audioContext.createMediaStreamDestination();

        // Добавляем аудио с вкладки (звук Meet)
        if (tabStream.getAudioTracks().length > 0) {
            const tabAudio = this.audioContext.createMediaStreamSource(tabStream);
            const tabGain = this.audioContext.createGain();
            tabGain.gain.value = 0.9; // Немного уменьшаем звук вкладки

            tabAudio.connect(tabGain);
            tabGain.connect(destination);

            console.log('Аудио вкладки подключено');
        }

        // Добавляем аудио с микрофона с обработкой для Bluetooth
        if (micStream && micStream.getAudioTracks().length > 0) {
            const micTrack = micStream.getAudioTracks()[0];
            console.log('Настройки микрофона:', micTrack.getSettings());

            const micAudio = this.audioContext.createMediaStreamSource(micStream);
            const micGain = this.audioContext.createGain();
            micGain.gain.value = 1.2; // Усиливаем микрофон

            // Добавляем компрессор для стабилизации уровня
            const compressor = this.audioContext.createDynamicsCompressor();
            compressor.threshold.value = -24;
            compressor.knee.value = 30;
            compressor.ratio.value = 12;
            compressor.attack.value = 0.003;
            compressor.release.value = 0.25;

            micAudio.connect(micGain);
            micGain.connect(compressor);
            compressor.connect(destination);

            console.log('Микрофон подключен с обработкой');
        }

        return destination.stream;
    }
    
    setupMediaRecorder(stream) {
        // Определяем поддерживаемый аудио формат
        let mimeType = 'audio/webm;codecs=opus';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'audio/webm';
        }
        if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'audio/mp4';
        }

        this.mediaRecorder = new MediaRecorder(stream, {
            mimeType: mimeType,
            audioBitsPerSecond: 128000
        });
        
        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                this.recordedChunks.push(event.data);
                console.log(`📦 Получен аудио чанк: ${event.data.size} байт`);
            }
        };

        this.mediaRecorder.onstop = () => {
            console.log('🛑 Запись остановлена, начинаем обработку...');
            this.processRecording();
        };
        
        this.mediaRecorder.onerror = (event) => {
            console.error('MediaRecorder error:', event.error);
            this.notifyError('Ошибка записи: ' + event.error.message);
        };
    }
    
    async stopRecording(sendResponse) {
        try {
            if (!this.isRecording) {
                if (sendResponse) sendResponse({ success: false, error: 'Запись не идет' });
                return;
            }

            await this.forceStopRecording();

            // Обновляем UI кнопки
            this.updateRecordButtonUI();

            if (sendResponse) sendResponse({ success: true });

        } catch (error) {
            console.error('Error stopping recording:', error);
            if (sendResponse) sendResponse({ success: false, error: error.message });
        }
    }
    
    async forceStopRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
            this.mediaRecorder.stop();
        }

        // Останавливаем все треки
        if (this.mixedStream) {
            this.mixedStream.getTracks().forEach(track => track.stop());
        }

        // Закрываем AudioContext
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }

        this.isRecording = false;
        this.startTime = null;
        this.mixedStream = null;

        // Обновляем UI кнопки
        this.updateRecordButtonUI();

        // Уведомляем background script
        chrome.runtime.sendMessage({ action: 'recordingStopped' });
    }

    async processRecording() {
        try {
            console.log('🔄 Обработка записи...');

            if (this.recordedChunks.length === 0) {
                console.warn('⚠️ Нет данных для обработки');
                return;
            }

            // Создаем blob из записанных чанков
            this.audioBlob = new Blob(this.recordedChunks, { type: 'audio/webm' });
            console.log(`📦 Создан аудио blob: ${this.audioBlob.size} байт`);

            // Сохраняем аудио файл
            await this.saveAudioFile();

            // Запускаем транскрипцию
            console.log('🤖 Запуск автоматической транскрипции...');
            await this.startTranscription();

        } catch (error) {
            console.error('❌ Ошибка обработки записи:', error);
            this.notifyError('Ошибка обработки записи: ' + error.message);
        }
    }

    async startTranscription() {
        try {
            chrome.runtime.sendMessage({
                action: 'transcriptUpdate',
                transcript: '',
                interim: '🤖 Отправка аудио на транскрипцию...'
            });

            const result = await this.transcriptionService.transcribeAudio(this.audioBlob);

            console.log('✅ Транскрипция получена:', result);
            this.transcript = result.text;

            // Уведомляем popup
            chrome.runtime.sendMessage({
                action: 'transcriptUpdate',
                transcript: this.transcript,
                interim: '✅ Транскрипция завершена!'
            });

            // Сохраняем транскрипт
            this.saveTranscriptFile(this.transcript, true);

        } catch (error) {
            console.error('❌ Ошибка транскрипции:', error);

            chrome.runtime.sendMessage({
                action: 'transcriptUpdate',
                transcript: '',
                interim: `❌ Ошибка транскрипции: ${error.message}`
            });

            // Сохраняем пустой транскрипт с информацией об ошибке
            this.saveTranscriptFile(`Ошибка транскрипции: ${error.message}`, false);
        }
    }

    async saveAudioFile() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `meet-recording-${timestamp}.webm`;

        const url = URL.createObjectURL(this.audioBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log(`💾 Аудио файл сохранен: ${filename}`);
    }

    saveTranscriptFile(transcript, hasContent) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `meet-transcript-${timestamp}.txt`;

        let content = `Транскрипция записи Google Meet\n`;
        content += `Дата: ${new Date().toLocaleString('ru-RU')}\n`;
        content += `Длительность: ${this.formatDuration(Date.now() - this.startTime)}\n\n`;
        content += `--- ТРАНСКРИПТ ---\n\n`;

        if (hasContent && transcript.trim()) {
            content += transcript;
        } else {
            content += `(Речь не была распознана)\n\n`;
            content += `Возможные причины:\n`;
            content += `- Не было речи во время записи\n`;
            content += `- Проблемы с микрофоном\n`;
            content += `- Проблемы с интернет соединением\n`;
            content += `- API транскрипции недоступен\n\n`;
            content += `Проверьте аудио файл для подтверждения содержимого.`;
        }

        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        setTimeout(() => URL.revokeObjectURL(url), 1000);

        console.log(`📄 Транскрипт сохранен: ${filename}`);

        // Уведомляем popup
        const wordCount = transcript ? transcript.split(/\s+/).filter(word => word.length > 0).length : 0;
        chrome.runtime.sendMessage({
            action: 'transcriptSaved',
            filename: filename,
            hasContent: hasContent,
            wordCount: wordCount
        });
    }

    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    async getMeetingInfo() {
        return new Promise((resolve) => {
            // Отправляем запрос в injected script для получения информации о встрече
            window.postMessage({ type: 'GET_MEET_STATE' }, '*');

            const handleResponse = (event) => {
                if (event.source === window && event.data.type === 'MEET_STATE_RESPONSE') {
                    window.removeEventListener('message', handleResponse);
                    resolve({
                        title: event.data.state.meetingTitle || '',
                        participantCount: event.data.state.participantCount || 0
                    });
                }
            };

            window.addEventListener('message', handleResponse);

            // Таймаут на случай если injected script не отвечает
            setTimeout(() => {
                window.removeEventListener('message', handleResponse);
                resolve({ title: '', participantCount: 0 });
            }, 1000);
        });
    }

    async saveRecordingMetadata(metadata) {
        try {
            // Сохраняем метаданные в chrome.storage
            const recordings = await chrome.storage.local.get('recordings') || { recordings: [] };
            recordings.recordings = recordings.recordings || [];

            recordings.recordings.push({
                id: Date.now().toString(),
                ...metadata,
                timestamp: new Date().toISOString()
            });

            // Ограничиваем количество сохраненных записей (последние 50)
            if (recordings.recordings.length > 50) {
                recordings.recordings = recordings.recordings.slice(-50);
            }

            await chrome.storage.local.set(recordings);

        } catch (error) {
            console.error('Ошибка сохранения метаданных:', error);
        }
    }

    showSaveNotification(filename, fileSize) {
        // Создаем временное уведомление
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            font-family: 'Segoe UI', sans-serif;
            font-size: 14px;
            max-width: 300px;
        `;

        const sizeInMB = (fileSize / (1024 * 1024)).toFixed(1);
        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">✅ Запись сохранена</div>
            <div style="font-size: 12px; opacity: 0.9;">
                ${filename}<br>
                Размер: ${sizeInMB} МБ
            </div>
        `;

        document.body.appendChild(notification);

        // Удаляем уведомление через 5 секунд
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    observeMeetChanges() {
        // Наблюдаем за изменениями в DOM для автоматической остановки записи
        const observer = new MutationObserver((mutations) => {
            // Проверяем, покинул ли пользователь встречу
            const meetEnded = document.querySelector('[data-call-ended="true"]') || 
                             document.querySelector('[aria-label*="покинуть встречу"]') ||
                             !document.querySelector('[data-meeting-title]');
            
            if (meetEnded && this.isRecording) {
                console.log('Встреча завершена, останавливаем запись');
                this.forceStopRecording();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true
        });
    }
    
    injectRecorderScript() {
        // Внедряем дополнительный скрипт для более глубокой интеграции
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('injected.js');
        script.onload = function() {
            this.remove();
        };
        (document.head || document.documentElement).appendChild(script);
    }

    injectRecordButton() {
        // Ждем загрузки интерфейса Meet
        const checkForToolbar = () => {
            // Ищем панель инструментов Meet (где кнопки микрофона, камеры и т.д.)
            const toolbar = document.querySelector('[data-call-controls-container="true"]') ||
                           document.querySelector('[jscontroller][jsaction*="click"]') ||
                           document.querySelector('div[role="toolbar"]') ||
                           document.querySelector('[data-self-name]')?.closest('div')?.querySelector('div[role="button"]')?.parentElement?.parentElement;

            if (toolbar) {
                this.createRecordButton(toolbar);
            } else {
                // Если не нашли, пробуем через 1 секунду
                setTimeout(checkForToolbar, 1000);
            }
        };

        // Начинаем поиск через 2 секунды после загрузки
        setTimeout(checkForToolbar, 2000);
    }

    createRecordButton(toolbar) {
        // Проверяем, не создана ли уже кнопка
        if (document.getElementById('meet-recorder-button')) {
            return;
        }

        console.log('🎯 Создание кнопки записи в Meet интерфейсе');

        // Создаем контейнер для кнопки
        const buttonContainer = document.createElement('div');
        buttonContainer.id = 'meet-recorder-button';
        buttonContainer.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        `;

        // Создаем кнопку записи
        const recordButton = document.createElement('button');
        recordButton.id = 'record-btn';
        recordButton.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <circle cx="12" cy="12" r="8" fill="#ff4444"/>
                <circle cx="12" cy="12" r="4" fill="white"/>
            </svg>
        `;
        recordButton.style.cssText = `
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;

        // Создаем индикатор статуса
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'record-status';
        statusIndicator.textContent = 'Начать запись';
        statusIndicator.style.cssText = `
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-family: 'Google Sans', sans-serif;
            white-space: nowrap;
            backdrop-filter: blur(10px);
        `;

        // Создаем таймер
        const timer = document.createElement('div');
        timer.id = 'record-timer';
        timer.style.cssText = `
            background: rgba(255, 68, 68, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-family: 'Google Sans', monospace;
            display: none;
            backdrop-filter: blur(10px);
        `;

        // Добавляем элементы в контейнер
        buttonContainer.appendChild(recordButton);
        buttonContainer.appendChild(statusIndicator);
        buttonContainer.appendChild(timer);

        // Добавляем в DOM
        document.body.appendChild(buttonContainer);

        // Обработчики событий
        recordButton.addEventListener('click', () => this.toggleRecording());

        // Hover эффекты
        recordButton.addEventListener('mouseenter', () => {
            recordButton.style.transform = 'scale(1.1)';
            recordButton.style.background = 'rgba(255, 255, 255, 0.2)';
        });

        recordButton.addEventListener('mouseleave', () => {
            recordButton.style.transform = 'scale(1)';
            recordButton.style.background = 'rgba(255, 255, 255, 0.1)';
        });

        console.log('✅ Кнопка записи создана');
        this.recordButton = recordButton;
        this.statusIndicator = statusIndicator;
        this.timerElement = timer;
    }

    async toggleRecording() {
        if (this.isRecording) {
            await this.stopRecording();
        } else {
            await this.startRecording();
        }
    }

    updateRecordButtonUI() {
        if (!this.recordButton) return;

        if (this.isRecording) {
            // Состояние записи
            this.recordButton.innerHTML = `
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <rect x="6" y="6" width="12" height="12" fill="white" rx="2"/>
                </svg>
            `;
            this.recordButton.style.background = 'rgba(255, 68, 68, 0.9)';
            this.statusIndicator.textContent = 'Остановить запись';
            this.statusIndicator.style.background = 'rgba(255, 68, 68, 0.9)';
            this.timerElement.style.display = 'block';

            // Запускаем таймер
            this.startTimer();
        } else {
            // Состояние готовности
            this.recordButton.innerHTML = `
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <circle cx="12" cy="12" r="8" fill="#ff4444"/>
                    <circle cx="12" cy="12" r="4" fill="white"/>
                </svg>
            `;
            this.recordButton.style.background = 'rgba(255, 255, 255, 0.1)';
            this.statusIndicator.textContent = 'Начать запись';
            this.statusIndicator.style.background = 'rgba(0, 0, 0, 0.8)';
            this.timerElement.style.display = 'none';

            // Останавливаем таймер
            this.stopTimer();
        }
    }

    startTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }

        this.timerInterval = setInterval(() => {
            if (this.startTime && this.timerElement) {
                const elapsed = Date.now() - this.startTime;
                const timeString = this.formatDuration(elapsed);
                this.timerElement.textContent = timeString;
            }
        }, 1000);
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    notifyError(error) {
        chrome.runtime.sendMessage({
            action: 'recordingError',
            error: error
        });
    }
}

// Инициализируем content script
if (window.location.hostname === 'meet.google.com') {
    const meetRecorder = new MeetRecorderContent();
}
