class MeetRecorderContent {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.startTime = null;
        this.audioContext = null;
        this.mixedStream = null;
        
        this.initEventListeners();
        this.injectRecorderScript();
    }
    
    initEventListeners() {
        // Слушаем сообщения от popup и background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
        
        // Отслеживаем изменения в DOM для детекции начала/окончания встречи
        this.observeMeetChanges();
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'startRecording':
                    await this.startRecording(sendResponse);
                    break;
                    
                case 'stopRecording':
                    await this.stopRecording(sendResponse);
                    break;
                    
                case 'getRecordingStatus':
                    sendResponse({
                        isRecording: this.isRecording,
                        startTime: this.startTime
                    });
                    break;
                    
                case 'forceStopRecording':
                    await this.forceStopRecording();
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async startRecording(sendResponse) {
        try {
            if (this.isRecording) {
                sendResponse({ success: false, error: 'Запись уже идет' });
                return;
            }
            
            // Получаем разрешение на захват экрана через background script
            const captureResponse = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'requestDesktopCapture' }, resolve);
            });
            
            if (!captureResponse.success) {
                throw new Error(captureResponse.error);
            }
            
            // Получаем поток с экрана и аудио
            const constraints = {
                audio: {
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: captureResponse.streamId
                    }
                },
                video: {
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: captureResponse.streamId
                    }
                }
            };
            
            const screenStream = await navigator.mediaDevices.getUserMedia(constraints);
            
            // Получаем поток с микрофона
            let micStream = null;
            try {
                micStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 48000
                    }
                });
            } catch (micError) {
                console.warn('Не удалось получить доступ к микрофону:', micError);
            }
            
            // Смешиваем аудио потоки
            this.mixedStream = await this.mixAudioStreams(screenStream, micStream);
            
            // Настраиваем MediaRecorder
            this.setupMediaRecorder(this.mixedStream);
            
            // Начинаем запись
            this.mediaRecorder.start(1000); // Сохраняем данные каждую секунду
            this.isRecording = true;
            this.startTime = Date.now();
            this.recordedChunks = [];
            
            // Уведомляем background script
            chrome.runtime.sendMessage({
                action: 'recordingStarted',
                startTime: this.startTime
            });
            
            sendResponse({ success: true });
            
        } catch (error) {
            console.error('Error starting recording:', error);
            this.notifyError(error.message);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async mixAudioStreams(screenStream, micStream) {
        // Создаем AudioContext для смешивания потоков
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        const destination = this.audioContext.createMediaStreamDestination();
        
        // Добавляем аудио с экрана (системный звук)
        if (screenStream.getAudioTracks().length > 0) {
            const screenAudio = this.audioContext.createMediaStreamSource(screenStream);
            screenAudio.connect(destination);
        }
        
        // Добавляем аудио с микрофона
        if (micStream && micStream.getAudioTracks().length > 0) {
            const micAudio = this.audioContext.createMediaStreamSource(micStream);
            micAudio.connect(destination);
        }
        
        // Добавляем видео поток
        const videoTrack = screenStream.getVideoTracks()[0];
        if (videoTrack) {
            destination.stream.addTrack(videoTrack);
        }
        
        return destination.stream;
    }
    
    setupMediaRecorder(stream) {
        // Определяем поддерживаемый формат
        let mimeType = 'video/webm;codecs=vp9,opus';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'video/webm;codecs=vp8,opus';
        }
        if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'video/webm';
        }
        
        this.mediaRecorder = new MediaRecorder(stream, {
            mimeType: mimeType,
            audioBitsPerSecond: 128000,
            videoBitsPerSecond: 2500000
        });
        
        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                this.recordedChunks.push(event.data);
            }
        };
        
        this.mediaRecorder.onstop = () => {
            this.saveRecording();
        };
        
        this.mediaRecorder.onerror = (event) => {
            console.error('MediaRecorder error:', event.error);
            this.notifyError('Ошибка записи: ' + event.error.message);
        };
    }
    
    async stopRecording(sendResponse) {
        try {
            if (!this.isRecording) {
                sendResponse({ success: false, error: 'Запись не идет' });
                return;
            }
            
            await this.forceStopRecording();
            sendResponse({ success: true });
            
        } catch (error) {
            console.error('Error stopping recording:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async forceStopRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
            this.mediaRecorder.stop();
        }
        
        // Останавливаем все треки
        if (this.mixedStream) {
            this.mixedStream.getTracks().forEach(track => track.stop());
        }
        
        // Закрываем AudioContext
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }
        
        this.isRecording = false;
        this.startTime = null;
        this.mixedStream = null;
        
        // Уведомляем background script
        chrome.runtime.sendMessage({ action: 'recordingStopped' });
    }
    
    saveRecording() {
        if (this.recordedChunks.length === 0) {
            console.warn('Нет данных для сохранения');
            return;
        }
        
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        
        // Создаем имя файла с временной меткой
        const now = new Date();
        const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
        const filename = `google-meet-recording-${timestamp}.webm`;
        
        // Создаем ссылку для скачивания
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // Освобождаем URL
        setTimeout(() => URL.revokeObjectURL(url), 1000);
        
        console.log(`Запись сохранена: ${filename}`);
    }
    
    observeMeetChanges() {
        // Наблюдаем за изменениями в DOM для автоматической остановки записи
        const observer = new MutationObserver((mutations) => {
            // Проверяем, покинул ли пользователь встречу
            const meetEnded = document.querySelector('[data-call-ended="true"]') || 
                             document.querySelector('[aria-label*="покинуть встречу"]') ||
                             !document.querySelector('[data-meeting-title]');
            
            if (meetEnded && this.isRecording) {
                console.log('Встреча завершена, останавливаем запись');
                this.forceStopRecording();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true
        });
    }
    
    injectRecorderScript() {
        // Внедряем дополнительный скрипт для более глубокой интеграции
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('injected.js');
        script.onload = function() {
            this.remove();
        };
        (document.head || document.documentElement).appendChild(script);
    }
    
    notifyError(error) {
        chrome.runtime.sendMessage({
            action: 'recordingError',
            error: error
        });
    }
}

// Инициализируем content script
if (window.location.hostname === 'meet.google.com') {
    const meetRecorder = new MeetRecorderContent();
}
