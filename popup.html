<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-indicator.recording {
            background: #ff4757;
            animation: pulse 1.5s infinite;
        }
        
        .status-indicator.ready {
            background: #2ed573;
        }
        
        .status-indicator.error {
            background: #ffa502;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .btn-start {
            background: #2ed573;
            color: white;
        }
        
        .btn-start:hover {
            background: #26d467;
        }
        
        .btn-stop {
            background: #ff4757;
            color: white;
        }
        
        .btn-stop:hover {
            background: #ff3838;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 10px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .recording-time {
            font-size: 16px;
            font-weight: 600;
            margin-top: 5px;
        }
        
        .error-message {
            background: rgba(255, 71, 87, 0.2);
            border: 1px solid #ff4757;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }

        .transcript-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .transcript-box {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .transcript-box::-webkit-scrollbar {
            width: 4px;
        }

        .transcript-box::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .transcript-box::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        /* Новые стили для истории записей */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .recordings-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .recording-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .recording-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .recording-item.active {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }

        .recording-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
        }

        .recording-title {
            font-weight: 500;
            color: white;
            font-size: 13px;
        }

        .recording-time {
            font-size: 11px;
            color: #aaa;
            font-family: monospace;
        }

        .recording-status {
            padding: 0 12px 8px;
            font-size: 11px;
            color: #ccc;
        }

        .recording-transcript {
            padding: 0 12px 12px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: 8px;
            display: none;
        }

        .recording-transcript.expanded {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 200px;
            }
        }

        .transcript-text {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            padding: 8px;
            font-size: 12px;
            line-height: 1.4;
            color: #e0e0e0;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .transcript-text::-webkit-scrollbar {
            width: 4px;
        }

        .transcript-text::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .transcript-text::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .transcript-meta {
            margin-top: 8px;
            font-size: 10px;
            color: #888;
            display: flex;
            justify-content: space-between;
        }

        .btn-clear {
            transition: color 0.2s ease;
        }

        .btn-clear:hover {
            color: #ff8a80 !important;
        }

        .no-recordings {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎥 Meet Recorder</h1>
    </div>
    
    <div class="status">
        <div id="status-text">
            <span class="status-indicator ready" id="status-indicator"></span>
            <span id="status-message">Готов к записи</span>
        </div>
        <div class="recording-time" id="recording-time" style="display: none;">00:00:00</div>
    </div>
    
    <div class="info-banner">
        <div style="text-align: center; margin: 15px 0; padding: 12px; background: rgba(76, 175, 80, 0.2); border-radius: 8px; border: 1px solid rgba(76, 175, 80, 0.3);">
            <div style="font-size: 14px; margin-bottom: 6px;">🎯 Управление записью</div>
            <div style="font-size: 12px; color: #ddd;">Используйте кнопку записи прямо в Google Meet!</div>
            <div style="font-size: 10px; color: #aaa; margin-top: 6px;">Кнопка появится в правом нижнем углу Meet</div>
        </div>
    </div>

    <div class="recordings-section">
        <div class="section-header">
            <h3 style="margin: 0; color: white; font-size: 16px;">📚 История записей</h3>
            <button class="btn-clear" id="clear-history" style="background: none; border: none; color: #ff6b6b; font-size: 12px; cursor: pointer;">🗑️ Очистить</button>
        </div>

        <div class="recordings-list" id="recordings-list">
            <div class="no-recordings" id="no-recordings" style="text-align: center; padding: 20px; color: #aaa; font-size: 13px;">
                📝 Записи появятся здесь после завершения
            </div>
        </div>
    </div>

    <div class="current-recording" id="current-recording" style="display: none;">
        <div class="recording-item active">
            <div class="recording-header">
                <span class="recording-title">🔴 Текущая запись</span>
                <span class="recording-time" id="current-time">00:00:00</span>
            </div>
            <div class="recording-status" id="current-status">Запись в процессе...</div>
        </div>
    </div>
    
    <div class="error-message" id="error-message" style="display: none;"></div>
    
    <script src="popup.js"></script>
</body>
</html>
