<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-indicator.recording {
            background: #ff4757;
            animation: pulse 1.5s infinite;
        }
        
        .status-indicator.ready {
            background: #2ed573;
        }
        
        .status-indicator.error {
            background: #ffa502;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .btn-start {
            background: #2ed573;
            color: white;
        }
        
        .btn-start:hover {
            background: #26d467;
        }
        
        .btn-stop {
            background: #ff4757;
            color: white;
        }
        
        .btn-stop:hover {
            background: #ff3838;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 10px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .recording-time {
            font-size: 16px;
            font-weight: 600;
            margin-top: 5px;
        }
        
        .error-message {
            background: rgba(255, 71, 87, 0.2);
            border: 1px solid #ff4757;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎥 Meet Recorder</h1>
    </div>
    
    <div class="status">
        <div id="status-text">
            <span class="status-indicator ready" id="status-indicator"></span>
            <span id="status-message">Готов к записи</span>
        </div>
        <div class="recording-time" id="recording-time" style="display: none;">00:00:00</div>
    </div>
    
    <div class="controls">
        <button class="btn btn-start" id="start-btn">Начать запись</button>
        <button class="btn btn-stop" id="stop-btn" disabled>Остановить</button>
    </div>

    <div class="controls" id="force-controls" style="display: none;">
        <button class="btn" id="force-stop-btn" style="background: #ff6b6b; font-size: 12px;">⚠️ Принудительная остановка</button>
    </div>
    
    <div class="info">
        <div>📍 Убедитесь, что вы находитесь на странице Google Meet</div>
        <div>🎧 Поддерживаются Bluetooth наушники</div>
        <div>💾 Записи сохраняются автоматически</div>
    </div>
    
    <div class="error-message" id="error-message" style="display: none;"></div>
    
    <script src="popup.js"></script>
</body>
</html>
