// Настройки расширения
class OptionsManager {
    constructor() {
        this.transcriptionService = new TranscriptionService();
        this.initElements();
        this.loadSettings();
        this.initEventListeners();
    }

    initElements() {
        this.serviceSelect = document.getElementById('transcription-service');
        this.apiKeyInput = document.getElementById('api-key');
        this.languageSelect = document.getElementById('language');
        this.audioQualitySelect = document.getElementById('audio-quality');
        this.autoTranscribeCheckbox = document.getElementById('auto-transcribe');
        this.saveAudioCheckbox = document.getElementById('save-audio');
        this.saveBtn = document.getElementById('save-btn');
        this.testBtn = document.getElementById('test-btn');
        this.statusDiv = document.getElementById('status');
        this.serviceInfo = document.getElementById('service-info');
    }

    initEventListeners() {
        this.serviceSelect.addEventListener('change', () => {
            this.updateServiceInfo();
            this.updateLanguageOptions();
        });

        this.saveBtn.addEventListener('click', () => this.saveSettings());
        this.testBtn.addEventListener('click', () => this.testConnection());

        // Показать/скрыть API ключ
        this.apiKeyInput.addEventListener('dblclick', () => {
            this.apiKeyInput.type = this.apiKeyInput.type === 'password' ? 'text' : 'password';
        });
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.local.get([
                'transcriptionApiKey',
                'transcriptionService',
                'transcriptionLanguage',
                'audioQuality',
                'autoTranscribe',
                'saveAudio'
            ]);

            this.serviceSelect.value = result.transcriptionService || 'openai';
            this.apiKeyInput.value = result.transcriptionApiKey || '';
            this.languageSelect.value = result.transcriptionLanguage || 'auto';
            this.audioQualitySelect.value = result.audioQuality || '128';
            this.autoTranscribeCheckbox.checked = result.autoTranscribe !== false;
            this.saveAudioCheckbox.checked = result.saveAudio !== false;

            this.updateServiceInfo();
            this.updateLanguageOptions();

        } catch (error) {
            console.error('Ошибка загрузки настроек:', error);
            this.showStatus('Ошибка загрузки настроек', 'error');
        }
    }

    async saveSettings() {
        try {
            const settings = {
                transcriptionApiKey: this.apiKeyInput.value.trim(),
                transcriptionService: this.serviceSelect.value,
                transcriptionLanguage: this.languageSelect.value,
                audioQuality: parseInt(this.audioQualitySelect.value),
                autoTranscribe: this.autoTranscribeCheckbox.checked,
                saveAudio: this.saveAudioCheckbox.checked
            };

            await chrome.storage.local.set(settings);
            
            // Обновляем сервис транскрипции
            await this.transcriptionService.saveSettings(
                settings.transcriptionApiKey,
                settings.transcriptionService
            );

            this.showStatus('Настройки сохранены успешно!', 'success');

        } catch (error) {
            console.error('Ошибка сохранения настроек:', error);
            this.showStatus('Ошибка сохранения настроек', 'error');
        }
    }

    async testConnection() {
        if (!this.apiKeyInput.value.trim()) {
            this.showStatus('Введите API ключ для тестирования', 'error');
            return;
        }

        this.testBtn.disabled = true;
        this.testBtn.textContent = 'Тестирование...';

        try {
            // Временно устанавливаем настройки для тестирования
            await this.transcriptionService.saveSettings(
                this.apiKeyInput.value.trim(),
                this.serviceSelect.value
            );

            await this.transcriptionService.testConnection();
            this.showStatus('✅ Подключение успешно! API ключ работает.', 'success');

        } catch (error) {
            console.error('Ошибка тестирования:', error);
            this.showStatus(`❌ Ошибка подключения: ${error.message}`, 'error');
        } finally {
            this.testBtn.disabled = false;
            this.testBtn.textContent = 'Тестировать подключение';
        }
    }

    updateServiceInfo() {
        const service = this.serviceSelect.value;
        
        const serviceInfos = {
            openai: {
                title: 'OpenAI Whisper API',
                description: `
                    <p>Для использования OpenAI Whisper API:</p>
                    <ol>
                        <li>Зарегистрируйтесь на <a href="https://platform.openai.com" target="_blank">platform.openai.com</a></li>
                        <li>Перейдите в раздел <a href="https://platform.openai.com/api-keys" target="_blank">API Keys</a></li>
                        <li>Создайте новый API ключ</li>
                        <li>Скопируйте ключ и вставьте его выше</li>
                    </ol>
                    <p><strong>Стоимость:</strong> ~$0.006 за минуту аудио</p>
                    <p><strong>Поддерживаемые языки:</strong> 99+ языков включая русский и английский</p>
                `
            },
            assemblyai: {
                title: 'AssemblyAI',
                description: `
                    <p>Для использования AssemblyAI:</p>
                    <ol>
                        <li>Зарегистрируйтесь на <a href="https://www.assemblyai.com" target="_blank">assemblyai.com</a></li>
                        <li>Перейдите в <a href="https://www.assemblyai.com/app" target="_blank">Dashboard</a></li>
                        <li>Скопируйте ваш API ключ</li>
                        <li>Вставьте ключ выше</li>
                    </ol>
                    <p><strong>Стоимость:</strong> ~$0.00037 за минуту аудио</p>
                    <p><strong>Поддерживаемые языки:</strong> 10+ языков включая русский и английский</p>
                `
            }
        };

        const info = serviceInfos[service];
        this.serviceInfo.innerHTML = `<h3>${info.title}</h3>${info.description}`;
    }

    updateLanguageOptions() {
        const service = this.serviceSelect.value;
        const currentValue = this.languageSelect.value;
        
        // Очищаем текущие опции
        this.languageSelect.innerHTML = '';
        
        // Получаем поддерживаемые языки для выбранного сервиса
        this.transcriptionService.serviceType = service;
        const languages = this.transcriptionService.getSupportedLanguages();
        
        // Добавляем опции
        languages.forEach(lang => {
            const option = document.createElement('option');
            option.value = lang.code;
            option.textContent = lang.name;
            this.languageSelect.appendChild(option);
        });
        
        // Восстанавливаем выбранное значение если возможно
        if ([...this.languageSelect.options].some(opt => opt.value === currentValue)) {
            this.languageSelect.value = currentValue;
        }
    }

    showStatus(message, type) {
        this.statusDiv.textContent = message;
        this.statusDiv.className = `status ${type}`;
        this.statusDiv.style.display = 'block';

        // Скрываем через 5 секунд
        setTimeout(() => {
            this.statusDiv.style.display = 'none';
        }, 5000);
    }
}

// Инициализируем когда DOM загружен
document.addEventListener('DOMContentLoaded', () => {
    new OptionsManager();
});
