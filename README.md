# Google Meet Audio Recorder & Transcriber

Chrome расширение для записи аудио Google Meet встреч с транскрибацией в реальном времени, включая поддержку Bluetooth устройств (AirPods и других).

## 🎯 Возможности

- ✅ **Запись только аудио** Google Meet встреч (без видео)
- ✅ **Транскрибация в реальном времени** (русский и английский языки)
- ✅ **Поддержка Bluetooth устройств** (AirPods, беспроводные наушники)
- ✅ **Смешивание аудио потоков** (звук встречи + микрофон)
- ✅ **Автоматическое сохранение** аудио и транскрипта
- ✅ **Детекция состояния встречи** (автоостановка при выходе)
- ✅ **Удобный интерфейс** с таймером и живой транскрибацией
- ✅ **Двойное сохранение** (аудио файл + текстовый транскрипт)

## 📋 Требования

- Google Chrome версии 88 или выше
- Доступ к микрофону и экрану
- Активная встреча в Google Meet

## 🚀 Установка

### Способ 1: Загрузка в режиме разработчика

1. **Скачайте файлы расширения** в папку на вашем компьютере

2. **Откройте Chrome** и перейдите в `chrome://extensions/`

3. **Включите режим разработчика** (переключатель в правом верхнем углу)

4. **Нажмите "Загрузить распакованное расширение"**

5. **Выберите папку** с файлами расширения

6. **Расширение установлено!** Иконка появится в панели инструментов

## 🎮 Использование

### Начало записи

1. **Откройте Google Meet** и присоединитесь к встрече
2. **Нажмите на иконку расширения** в панели инструментов Chrome
3. **Нажмите "Начать запись"**
4. **Разрешите доступ** к экрану и микрофону при запросе
5. **Запись началась!** Индикатор покажет статус записи

### Остановка записи

1. **Нажмите на иконку расширения**
2. **Нажмите "Остановить"**
3. **Файл автоматически сохранится** в папку загрузок

### Особенности для Bluetooth устройств

- **AirPods и другие Bluetooth наушники поддерживаются**
- **Расширение автоматически определит** Bluetooth микрофон
- **Качество звука оптимизировано** для беспроводных устройств
- **Компрессор и усиление** для стабильного уровня звука

## 📁 Структура файлов

```
googleMeetExt/
├── manifest.json          # Конфигурация расширения
├── popup.html            # Интерфейс управления
├── popup.js              # Логика интерфейса
├── background.js         # Фоновый сервис-воркер
├── content.js            # Скрипт для страницы Meet
├── injected.js           # Глубокая интеграция с Meet
└── README.md             # Документация
```

## 🔧 Технические детали

### Поддерживаемые форматы
- **Аудио**: WebM с кодеком Opus, 48kHz, стерео
- **Транскрипт**: Текстовый файл UTF-8
- **Качество**: 128 kbps аудио

### Разрешения
- `activeTab` - доступ к активной вкладке
- `storage` - сохранение метаданных и транскриптов
- `tabCapture` - захват аудио вкладки
- `scripting` - внедрение скриптов

### Аудио обработка
- **Смешивание потоков** через Web Audio API
- **Компрессор** для стабилизации уровня
- **Усиление микрофона** для Bluetooth устройств
- **Подавление эха и шума**

### Транскрибация
- **Web Speech API** для распознавания речи
- **Поддержка языков**: русский (ru-RU) и английский (en-US)
- **Автоматическое переключение** языков при ошибках
- **Сохранение с временными метками**

## 🐛 Устранение неполадок

### Не работает запись
1. Убедитесь, что вы на странице `meet.google.com`
2. Проверьте разрешения для микрофона и экрана
3. Перезагрузите страницу Meet
4. Перезапустите Chrome

### Проблемы с Bluetooth аудио
1. Убедитесь, что устройство подключено к системе
2. Проверьте, что устройство выбрано как микрофон по умолчанию
3. Попробуйте переподключить Bluetooth устройство
4. Проверьте настройки звука в Chrome

### Низкое качество звука
1. Проверьте настройки микрофона в системе
2. Убедитесь в стабильном Bluetooth соединении
3. Закройте другие приложения, использующие микрофон
4. Попробуйте изменить качество в настройках Meet

## 📝 Примечания

- **Записи сохраняются локально** в папку загрузок
- **Метаданные хранятся** в локальном хранилище Chrome
- **Автоматическая остановка** при выходе из встречи
- **Поддержка только Google Meet** (не Zoom, Teams и др.)

## 🔒 Приватность

- Расширение **не отправляет данные** на внешние серверы
- Все записи **остаются на вашем устройстве**
- **Никакой телеметрии** или отслеживания
- Доступ только к **активным вкладкам Meet**

## 🆘 Поддержка

При возникновении проблем:
1. Проверьте консоль разработчика (F12)
2. Убедитесь в актуальности версии Chrome
3. Проверьте разрешения расширения
4. Попробуйте переустановить расширение
