<!DOCTYPE html>
<html>
<head>
    <title>Тест микрофона и Speech Recognition</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .transcript-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .interim {
            color: #6c757d;
            font-style: italic;
        }
        
        .final {
            color: #212529;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Тест микрофона и транскрибации</h1>
        
        <div class="test-section">
            <h3>1. Проверка доступа к микрофону</h3>
            <button onclick="testMicrophone()">Тест микрофона</button>
            <div id="micResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Тест Speech Recognition (Русский)</h3>
            <button onclick="testSpeechRussian()" id="ruBtn">Тест на русском</button>
            <button onclick="stopRecognition()" id="stopBtn" disabled>Остановить</button>
            <div id="ruResult" class="result" style="display: none;"></div>
            <div class="transcript-box" id="ruTranscript" style="display: none;">
                <div><strong>Транскрипт:</strong></div>
                <div id="ruFinal" class="final"></div>
                <div id="ruInterim" class="interim"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. Тест Speech Recognition (English)</h3>
            <button onclick="testSpeechEnglish()" id="enBtn">Test in English</button>
            <div id="enResult" class="result" style="display: none;"></div>
            <div class="transcript-box" id="enTranscript" style="display: none;">
                <div><strong>Transcript:</strong></div>
                <div id="enFinal" class="final"></div>
                <div id="enInterim" class="interim"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. Информация о браузере</h3>
            <button onclick="showBrowserInfo()">Показать информацию</button>
            <div id="browserInfo" class="result" style="display: none;"></div>
        </div>
    </div>
    
    <script>
        let currentRecognition = null;
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }
        
        async function testMicrophone() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });
                
                const tracks = stream.getAudioTracks();
                if (tracks.length > 0) {
                    const track = tracks[0];
                    const settings = track.getSettings();
                    
                    showResult('micResult', 
                        `✅ Микрофон работает!\n\n` +
                        `Устройство: ${track.label}\n` +
                        `Sample Rate: ${settings.sampleRate}Hz\n` +
                        `Channel Count: ${settings.channelCount}\n` +
                        `Echo Cancellation: ${settings.echoCancellation}\n` +
                        `Noise Suppression: ${settings.noiseSuppression}`, 
                        'success');
                    
                    // Останавливаем поток
                    stream.getTracks().forEach(track => track.stop());
                } else {
                    showResult('micResult', '❌ Микрофон недоступен', 'error');
                }
            } catch (error) {
                showResult('micResult', `❌ Ошибка доступа к микрофону:\n${error.message}`, 'error');
            }
        }
        
        function testSpeechRussian() {
            testSpeechRecognition('ru-RU', 'ruResult', 'ruTranscript', 'ruFinal', 'ruInterim');
        }
        
        function testSpeechEnglish() {
            testSpeechRecognition('en-US', 'enResult', 'enTranscript', 'enFinal', 'enInterim');
        }
        
        function testSpeechRecognition(lang, resultId, transcriptId, finalId, interimId) {
            try {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                if (!SpeechRecognition) {
                    showResult(resultId, '❌ Speech Recognition API не поддерживается', 'error');
                    return;
                }
                
                // Останавливаем предыдущее распознавание
                if (currentRecognition) {
                    currentRecognition.stop();
                }
                
                currentRecognition = new SpeechRecognition();
                currentRecognition.continuous = true;
                currentRecognition.interimResults = true;
                currentRecognition.lang = lang;
                
                let finalTranscript = '';
                
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('ruBtn').disabled = true;
                document.getElementById('enBtn').disabled = true;
                
                currentRecognition.onstart = () => {
                    showResult(resultId, `🎤 Слушаю на ${lang === 'ru-RU' ? 'русском' : 'английском'}...\nГоворите что-нибудь!`, 'info');
                    document.getElementById(transcriptId).style.display = 'block';
                };
                
                currentRecognition.onresult = (event) => {
                    let interimTranscript = '';
                    
                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        const transcript = event.results[i][0].transcript;
                        const confidence = event.results[i][0].confidence;
                        
                        if (event.results[i].isFinal) {
                            finalTranscript += transcript + ' ';
                            document.getElementById(finalId).textContent = finalTranscript;
                        } else {
                            interimTranscript += transcript;
                        }
                    }
                    
                    document.getElementById(interimId).textContent = interimTranscript;
                    
                    if (finalTranscript.trim()) {
                        showResult(resultId, `✅ Распознавание работает!\nТекст: "${finalTranscript.trim()}"`, 'success');
                    }
                };
                
                currentRecognition.onerror = (event) => {
                    let errorMsg = `❌ Ошибка: ${event.error}`;
                    
                    switch(event.error) {
                        case 'no-speech':
                            errorMsg += '\n\nРечь не обнаружена. Попробуйте:\n• Говорить громче\n• Подойти ближе к микрофону\n• Проверить настройки микрофона';
                            break;
                        case 'audio-capture':
                            errorMsg += '\n\nПроблема с захватом аудио. Проверьте микрофон.';
                            break;
                        case 'not-allowed':
                            errorMsg += '\n\nДоступ запрещен. Разрешите доступ к микрофону.';
                            break;
                        case 'network':
                            errorMsg += '\n\nПроблема с сетью. Проверьте интернет.';
                            break;
                    }
                    
                    showResult(resultId, errorMsg, 'error');
                    resetButtons();
                };
                
                currentRecognition.onend = () => {
                    if (!finalTranscript.trim()) {
                        showResult(resultId, '❌ Речь не распознана', 'error');
                    }
                    resetButtons();
                };
                
                currentRecognition.start();
                
            } catch (error) {
                showResult(resultId, `❌ Критическая ошибка: ${error.message}`, 'error');
                resetButtons();
            }
        }
        
        function stopRecognition() {
            if (currentRecognition) {
                currentRecognition.stop();
                currentRecognition = null;
            }
            resetButtons();
        }
        
        function resetButtons() {
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('ruBtn').disabled = false;
            document.getElementById('enBtn').disabled = false;
        }
        
        function showBrowserInfo() {
            const info = `Браузер: ${navigator.userAgent}\n\n` +
                        `Speech Recognition: ${window.SpeechRecognition ? 'Поддерживается' : 'Не поддерживается'}\n` +
                        `webkitSpeechRecognition: ${window.webkitSpeechRecognition ? 'Поддерживается' : 'Не поддерживается'}\n` +
                        `MediaDevices: ${navigator.mediaDevices ? 'Поддерживается' : 'Не поддерживается'}\n` +
                        `getUserMedia: ${navigator.mediaDevices?.getUserMedia ? 'Поддерживается' : 'Не поддерживается'}\n\n` +
                        `Протокол: ${location.protocol}\n` +
                        `Хост: ${location.host}`;
            
            showResult('browserInfo', info, 'info');
        }
    </script>
</body>
</html>
