<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Meet Recorder - Настройки</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .section h2 {
            margin-top: 0;
            color: #555;
            font-size: 18px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        select, input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-size: 13px;
            color: #1565c0;
        }

        .info h3 {
            margin-top: 0;
            color: #1565c0;
        }

        .info a {
            color: #1565c0;
            text-decoration: none;
        }

        .info a:hover {
            text-decoration: underline;
        }

        .api-key-input {
            font-family: monospace;
            font-size: 12px;
        }

        .language-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .language-item {
            padding: 8px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Meet Recorder - Настройки</h1>

        <div class="section">
            <h2>Сервис транскрипции</h2>
            
            <div class="form-group">
                <label for="transcription-service">Выберите сервис:</label>
                <select id="transcription-service">
                    <option value="openai">OpenAI Whisper (рекомендуется)</option>
                    <option value="assemblyai">AssemblyAI</option>
                </select>
            </div>

            <div class="form-group">
                <label for="api-key">API ключ:</label>
                <input type="password" id="api-key" class="api-key-input" placeholder="Введите ваш API ключ">
            </div>

            <div class="form-group">
                <label for="language">Язык транскрипции:</label>
                <select id="language">
                    <option value="auto">Автоопределение</option>
                    <option value="ru">Русский</option>
                    <option value="en">English</option>
                </select>
            </div>

            <button class="btn" id="save-btn">Сохранить настройки</button>
            <button class="btn btn-secondary" id="test-btn">Тестировать подключение</button>

            <div class="status" id="status"></div>

            <div class="info" id="service-info">
                <h3>OpenAI Whisper API</h3>
                <p>Для использования OpenAI Whisper API:</p>
                <ol>
                    <li>Зарегистрируйтесь на <a href="https://platform.openai.com" target="_blank">platform.openai.com</a></li>
                    <li>Перейдите в раздел <a href="https://platform.openai.com/api-keys" target="_blank">API Keys</a></li>
                    <li>Создайте новый API ключ</li>
                    <li>Скопируйте ключ и вставьте его выше</li>
                </ol>
                <p><strong>Стоимость:</strong> ~$0.006 за минуту аудио</p>
                <p><strong>Поддерживаемые языки:</strong> 99+ языков включая русский и английский</p>
            </div>
        </div>

        <div class="section">
            <h2>Настройки записи</h2>
            
            <div class="form-group">
                <label for="audio-quality">Качество аудио:</label>
                <select id="audio-quality">
                    <option value="128">Стандартное (128 kbps)</option>
                    <option value="192">Высокое (192 kbps)</option>
                    <option value="256">Максимальное (256 kbps)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="auto-transcribe">
                    <input type="checkbox" id="auto-transcribe" checked>
                    Автоматически транскрибировать после записи
                </label>
            </div>

            <div class="form-group">
                <label for="save-audio">
                    <input type="checkbox" id="save-audio" checked>
                    Сохранять аудио файлы
                </label>
            </div>
        </div>

        <div class="section">
            <h2>О расширении</h2>
            <p>Meet Recorder позволяет записывать аудио из Google Meet и автоматически создавать транскрипты с помощью современных AI сервисов.</p>
            <p><strong>Версия:</strong> 1.0.0</p>
            <p><strong>Поддержка:</strong> Русский и английский языки</p>
        </div>
    </div>

    <script src="transcription-service.js"></script>
    <script src="options.js"></script>
</body>
</html>
