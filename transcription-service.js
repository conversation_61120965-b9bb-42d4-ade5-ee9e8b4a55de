class TranscriptionService {
    constructor() {
        this.apiKey = '********************************************************************************************************************************************************************';

        if (this.apiKey === '********************************************************************************************************************************************************************') {
            console.error('❌ Не установлен API ключ OpenAI! Отредактируйте transcription-service.js');
        }
    }

    async transcribeAudio(audioBlob) {
        if (this.apiKey === '********************************************************************************************************************************************************************') {
            throw new Error('API ключ не установлен. Отредактируйте transcription-service.js');
        }

        return await this.transcribeWithOpenAI(audioBlob);
    }

    async transcribeWithOpenAI(audioBlob) {
        try {
            console.log('🤖 Отправка аудио в OpenAI Whisper...');

            const formData = new FormData();
            formData.append('file', audioBlob, 'audio.webm');
            formData.append('model', 'whisper-1');
            formData.append('language', 'ru'); 
            formData.append('response_format', 'json');

            const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`OpenAI API ошибка: ${response.status} - ${errorData.error?.message || response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ Транскрипция получена от OpenAI');

            return {
                text: result.text,
                language: result.language || 'ru',
                service: 'openai'
            };

        } catch (error) {
            console.error('❌ Ошибка транскрипции OpenAI:', error);
            throw error;
        }
    }

}

// Экспортируем для использования в других скриптах
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TranscriptionService;
} else {
    window.TranscriptionService = TranscriptionService;
}
