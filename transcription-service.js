// Сервис для транскрипции аудио через внешние API
class TranscriptionService {
    constructor() {
        this.apiKey = null;
        this.serviceType = 'openai'; // openai, assemblyai, speechmatics
        this.loadSettings();
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['transcriptionApiKey', 'transcriptionService']);
            this.apiKey = result.transcriptionApiKey;
            this.serviceType = result.transcriptionService || 'openai';
        } catch (error) {
            console.error('Ошибка загрузки настроек транскрипции:', error);
        }
    }

    async saveSettings(apiKey, serviceType = 'openai') {
        try {
            await chrome.storage.local.set({
                transcriptionApiKey: apiKey,
                transcriptionService: serviceType
            });
            this.apiKey = apiKey;
            this.serviceType = serviceType;
        } catch (error) {
            console.error('Ошибка сохранения настроек транскрипции:', error);
        }
    }

    async transcribeAudio(audioBlob, options = {}) {
        if (!this.apiKey) {
            throw new Error('API ключ не настроен. Откройте настройки расширения.');
        }

        switch (this.serviceType) {
            case 'openai':
                return await this.transcribeWithOpenAI(audioBlob, options);
            case 'assemblyai':
                return await this.transcribeWithAssemblyAI(audioBlob, options);
            default:
                throw new Error(`Неподдерживаемый сервис: ${this.serviceType}`);
        }
    }

    async transcribeWithOpenAI(audioBlob, options = {}) {
        try {
            console.log('🤖 Отправка аудио в OpenAI Whisper...');
            
            const formData = new FormData();
            formData.append('file', audioBlob, 'audio.webm');
            formData.append('model', 'whisper-1');
            formData.append('language', options.language || 'ru'); // ru, en, auto
            formData.append('response_format', 'verbose_json');
            formData.append('timestamp_granularities[]', 'word');

            const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`OpenAI API ошибка: ${response.status} - ${errorData.error?.message || response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ Транскрипция получена от OpenAI');
            
            return {
                text: result.text,
                words: result.words || [],
                language: result.language,
                duration: result.duration,
                service: 'openai'
            };

        } catch (error) {
            console.error('❌ Ошибка транскрипции OpenAI:', error);
            throw error;
        }
    }

    async transcribeWithAssemblyAI(audioBlob, options = {}) {
        try {
            console.log('🤖 Отправка аудио в AssemblyAI...');
            
            // Сначала загружаем файл
            const uploadResponse = await fetch('https://api.assemblyai.com/v2/upload', {
                method: 'POST',
                headers: {
                    'authorization': this.apiKey,
                    'content-type': 'application/octet-stream'
                },
                body: audioBlob
            });

            if (!uploadResponse.ok) {
                throw new Error(`AssemblyAI upload ошибка: ${uploadResponse.status}`);
            }

            const { upload_url } = await uploadResponse.json();

            // Запускаем транскрипцию
            const transcriptResponse = await fetch('https://api.assemblyai.com/v2/transcript', {
                method: 'POST',
                headers: {
                    'authorization': this.apiKey,
                    'content-type': 'application/json'
                },
                body: JSON.stringify({
                    audio_url: upload_url,
                    language_code: options.language === 'en' ? 'en' : 'ru',
                    punctuate: true,
                    format_text: true
                })
            });

            if (!transcriptResponse.ok) {
                throw new Error(`AssemblyAI transcript ошибка: ${transcriptResponse.status}`);
            }

            const transcript = await transcriptResponse.json();
            
            // Ждем завершения транскрипции
            return await this.pollAssemblyAIResult(transcript.id);

        } catch (error) {
            console.error('❌ Ошибка транскрипции AssemblyAI:', error);
            throw error;
        }
    }

    async pollAssemblyAIResult(transcriptId) {
        const maxAttempts = 60; // 5 минут максимум
        let attempts = 0;

        while (attempts < maxAttempts) {
            const response = await fetch(`https://api.assemblyai.com/v2/transcript/${transcriptId}`, {
                headers: {
                    'authorization': this.apiKey
                }
            });

            const result = await response.json();

            if (result.status === 'completed') {
                console.log('✅ Транскрипция получена от AssemblyAI');
                return {
                    text: result.text,
                    words: result.words || [],
                    language: result.language_code,
                    duration: result.audio_duration,
                    service: 'assemblyai'
                };
            } else if (result.status === 'error') {
                throw new Error(`AssemblyAI ошибка: ${result.error}`);
            }

            // Ждем 5 секунд перед следующей проверкой
            await new Promise(resolve => setTimeout(resolve, 5000));
            attempts++;
        }

        throw new Error('Таймаут транскрипции AssemblyAI');
    }

    // Проверка доступности сервиса
    async testConnection() {
        if (!this.apiKey) {
            throw new Error('API ключ не настроен');
        }

        try {
            // Создаем тестовый аудио файл (1 секунда тишины)
            const testBlob = this.createSilentAudio();
            
            switch (this.serviceType) {
                case 'openai':
                    await this.transcribeWithOpenAI(testBlob, { language: 'en' });
                    break;
                case 'assemblyai':
                    await this.transcribeWithAssemblyAI(testBlob, { language: 'en' });
                    break;
            }
            
            return true;
        } catch (error) {
            console.error('Тест подключения не прошел:', error);
            throw error;
        }
    }

    createSilentAudio() {
        // Создаем минимальный WebM файл с тишиной для тестирования
        const arrayBuffer = new ArrayBuffer(1024);
        return new Blob([arrayBuffer], { type: 'audio/webm' });
    }

    // Получение поддерживаемых языков
    getSupportedLanguages() {
        const languages = {
            openai: [
                { code: 'auto', name: 'Автоопределение' },
                { code: 'ru', name: 'Русский' },
                { code: 'en', name: 'English' },
                { code: 'es', name: 'Español' },
                { code: 'fr', name: 'Français' },
                { code: 'de', name: 'Deutsch' },
                { code: 'it', name: 'Italiano' },
                { code: 'pt', name: 'Português' },
                { code: 'zh', name: '中文' },
                { code: 'ja', name: '日本語' },
                { code: 'ko', name: '한국어' }
            ],
            assemblyai: [
                { code: 'en', name: 'English' },
                { code: 'ru', name: 'Русский' },
                { code: 'es', name: 'Español' },
                { code: 'fr', name: 'Français' },
                { code: 'de', name: 'Deutsch' },
                { code: 'it', name: 'Italiano' },
                { code: 'pt', name: 'Português' },
                { code: 'nl', name: 'Nederlands' },
                { code: 'hi', name: 'हिन्दी' },
                { code: 'ja', name: '日本語' }
            ]
        };

        return languages[this.serviceType] || languages.openai;
    }
}

// Экспортируем для использования в других скриптах
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TranscriptionService;
} else {
    window.TranscriptionService = TranscriptionService;
}
