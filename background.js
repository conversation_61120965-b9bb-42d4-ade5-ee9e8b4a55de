class MeetRecorderBackground {
    constructor() {
        this.recordingState = {
            isRecording: false,
            tabId: null,
            streamId: null,
            startTime: null
        };
        
        this.initEventListeners();
    }
    
    initEventListeners() {
        // Слушаем сообщения от content script и popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Указывает, что ответ будет асинхронным
        });
        
        // Обрабатываем закрытие вкладок
        chrome.tabs.onRemoved.addListener((tabId) => {
            if (this.recordingState.tabId === tabId && this.recordingState.isRecording) {
                this.stopRecording();
            }
        });
        
        // Обрабатываем обновление вкладок
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (this.recordingState.tabId === tabId && 
                this.recordingState.isRecording && 
                changeInfo.url && 
                !changeInfo.url.includes('meet.google.com')) {
                this.stopRecording();
            }
        });
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'requestDesktopCapture':
                    await this.handleDesktopCaptureRequest(message, sender, sendResponse);
                    break;
                    
                case 'recordingStarted':
                    this.recordingState.isRecording = true;
                    this.recordingState.tabId = sender.tab.id;
                    this.recordingState.startTime = Date.now();
                    
                    // Уведомляем popup
                    this.notifyPopup('recordingStarted', { startTime: this.recordingState.startTime });
                    sendResponse({ success: true });
                    break;
                    
                case 'recordingStopped':
                    this.recordingState.isRecording = false;
                    this.recordingState.tabId = null;
                    this.recordingState.streamId = null;
                    this.recordingState.startTime = null;
                    
                    // Уведомляем popup
                    this.notifyPopup('recordingStopped');
                    sendResponse({ success: true });
                    break;
                    
                case 'getRecordingState':
                    sendResponse(this.recordingState);
                    break;
                    
                case 'recordingError':
                    this.recordingState.isRecording = false;
                    this.recordingState.tabId = null;
                    this.recordingState.streamId = null;
                    
                    // Уведомляем popup об ошибке
                    this.notifyPopup('recordingError', { error: message.error });
                    sendResponse({ success: false, error: message.error });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async handleDesktopCaptureRequest(message, sender, sendResponse) {
        try {
            // Запрашиваем разрешение на захват экрана
            const streamId = await new Promise((resolve, reject) => {
                chrome.desktopCapture.chooseDesktopMedia(
                    ['tab', 'audio'],
                    sender.tab,
                    (streamId) => {
                        if (streamId) {
                            resolve(streamId);
                        } else {
                            reject(new Error('Пользователь отменил выбор источника'));
                        }
                    }
                );
            });
            
            this.recordingState.streamId = streamId;
            sendResponse({ success: true, streamId: streamId });
            
        } catch (error) {
            console.error('Desktop capture error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async notifyPopup(action, data = {}) {
        try {
            // Получаем все окна расширения (popup)
            const views = chrome.extension.getViews({ type: 'popup' });
            
            // Отправляем сообщение в popup если он открыт
            if (views.length > 0) {
                chrome.runtime.sendMessage({
                    action: action,
                    ...data
                });
            }
        } catch (error) {
            console.error('Error notifying popup:', error);
        }
    }
    
    stopRecording() {
        if (this.recordingState.isRecording && this.recordingState.tabId) {
            chrome.tabs.sendMessage(this.recordingState.tabId, { action: 'forceStopRecording' });
        }
        
        this.recordingState.isRecording = false;
        this.recordingState.tabId = null;
        this.recordingState.streamId = null;
        this.recordingState.startTime = null;
    }
}

// Инициализируем background script
const meetRecorderBackground = new MeetRecorderBackground();
