<!DOCTYPE html>
<html>
<head>
    <title>Google Meet Recorder - Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Google Meet Recorder - Тестирование</h1>
        
        <div class="test-section">
            <h3>1. Проверка поддержки MediaRecorder API</h3>
            <button onclick="testMediaRecorder()">Тест MediaRecorder</button>
            <div id="mediaRecorderResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Проверка доступа к микрофону</h3>
            <button onclick="testMicrophone()">Тест микрофона</button>
            <div id="microphoneResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Проверка Web Audio API</h3>
            <button onclick="testWebAudio()">Тест Web Audio</button>
            <div id="webAudioResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Проверка доступных аудио устройств</h3>
            <button onclick="testAudioDevices()">Показать устройства</button>
            <div id="audioDevicesResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>5. Тест Speech Recognition API</h3>
            <button onclick="testSpeechRecognition()" id="speechBtn">Тест транскрибации</button>
            <div id="speechResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>6. Тест аудио записи (5 секунд)</h3>
            <button onclick="testAudioRecording()" id="recordBtn">Начать тест записи</button>
            <div id="recordingResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📋 Инструкции по установке</h3>
            <ol>
                <li>Откройте Chrome и перейдите в <code>chrome://extensions/</code></li>
                <li>Включите "Режим разработчика" в правом верхнем углу</li>
                <li>Нажмите "Загрузить распакованное расширение"</li>
                <li>Выберите папку с файлами расширения</li>
                <li>Перейдите на <code>meet.google.com</code> и протестируйте</li>
            </ol>
        </div>
    </div>
    
    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }
        
        function testMediaRecorder() {
            try {
                if (typeof MediaRecorder === 'undefined') {
                    showResult('mediaRecorderResult', 'MediaRecorder API не поддерживается', 'error');
                    return;
                }
                
                const mimeTypes = [
                    'video/webm;codecs=vp9,opus',
                    'video/webm;codecs=vp8,opus',
                    'video/webm',
                    'audio/webm'
                ];
                
                const supported = mimeTypes.filter(type => MediaRecorder.isTypeSupported(type));
                
                if (supported.length > 0) {
                    showResult('mediaRecorderResult', 
                        `MediaRecorder поддерживается!\nПоддерживаемые форматы: ${supported.join(', ')}`, 
                        'success');
                } else {
                    showResult('mediaRecorderResult', 'MediaRecorder не поддерживает нужные форматы', 'error');
                }
            } catch (error) {
                showResult('mediaRecorderResult', `Ошибка: ${error.message}`, 'error');
            }
        }
        
        async function testMicrophone() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                const tracks = stream.getAudioTracks();
                
                if (tracks.length > 0) {
                    const track = tracks[0];
                    showResult('microphoneResult', 
                        `Микрофон доступен!\nУстройство: ${track.label}\nНастройки: ${JSON.stringify(track.getSettings(), null, 2)}`, 
                        'success');
                    
                    // Останавливаем поток
                    stream.getTracks().forEach(track => track.stop());
                } else {
                    showResult('microphoneResult', 'Микрофон недоступен', 'error');
                }
            } catch (error) {
                showResult('microphoneResult', `Ошибка доступа к микрофону: ${error.message}`, 'error');
            }
        }
        
        function testWebAudio() {
            try {
                const AudioContext = window.AudioContext || window.webkitAudioContext;
                if (!AudioContext) {
                    showResult('webAudioResult', 'Web Audio API не поддерживается', 'error');
                    return;
                }
                
                const audioContext = new AudioContext();
                showResult('webAudioResult', 
                    `Web Audio API поддерживается!\nSample Rate: ${audioContext.sampleRate}Hz\nState: ${audioContext.state}`, 
                    'success');
                
                audioContext.close();
            } catch (error) {
                showResult('webAudioResult', `Ошибка Web Audio API: ${error.message}`, 'error');
            }
        }
        
        async function testAudioDevices() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                
                if (audioInputs.length > 0) {
                    const deviceList = audioInputs.map(device => 
                        `${device.label || 'Неизвестное устройство'} (ID: ${device.deviceId.substring(0, 8)}...)`
                    ).join('\n');
                    
                    const bluetoothDevices = audioInputs.filter(device => 
                        device.label.toLowerCase().includes('airpods') ||
                        device.label.toLowerCase().includes('bluetooth') ||
                        device.label.toLowerCase().includes('wireless')
                    );
                    
                    let message = `Найдено аудио устройств: ${audioInputs.length}\n\n${deviceList}`;
                    
                    if (bluetoothDevices.length > 0) {
                        message += `\n\n🎧 Bluetooth устройства найдены: ${bluetoothDevices.length}`;
                    }
                    
                    showResult('audioDevicesResult', message, 'success');
                } else {
                    showResult('audioDevicesResult', 'Аудио устройства не найдены', 'error');
                }
            } catch (error) {
                showResult('audioDevicesResult', `Ошибка получения устройств: ${error.message}`, 'error');
            }
        }
        
        async function testSpeechRecognition() {
            try {
                // Сначала проверяем доступность API
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                if (!SpeechRecognition) {
                    showResult('speechResult', 'Speech Recognition API не поддерживается в этом браузере', 'error');
                    return;
                }

                // Запрашиваем разрешение на микрофон
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    stream.getTracks().forEach(track => track.stop()); // Останавливаем поток
                    showResult('speechResult', 'Разрешение на микрофон получено. Инициализация...', 'info');
                } catch (micError) {
                    showResult('speechResult', `Нет доступа к микрофону: ${micError.message}`, 'error');
                    return;
                }

                const recognition = new SpeechRecognition();
                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.maxAlternatives = 1;
                recognition.lang = 'ru-RU';

                let finalTranscript = '';
                let hasResults = false;

                recognition.onstart = () => {
                    showResult('speechResult', '🎤 Слушаю... Говорите что-нибудь (10 секунд)\nПопробуйте сказать: "Привет, как дела?" или "Hello, how are you?"', 'info');
                };

                recognition.onresult = (event) => {
                    hasResults = true;
                    let interimTranscript = '';

                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        const transcript = event.results[i][0].transcript;
                        const confidence = event.results[i][0].confidence;

                        if (event.results[i].isFinal) {
                            finalTranscript += transcript + ' ';
                        } else {
                            interimTranscript += transcript;
                        }
                    }

                    let result = '✅ Транскрибация работает!\n\n';
                    if (finalTranscript) {
                        result += `Финальный текст: "${finalTranscript.trim()}"\n`;
                    }
                    if (interimTranscript) {
                        result += `Распознается: "${interimTranscript.trim()}"`;
                    }

                    showResult('speechResult', result, 'success');
                };

                recognition.onerror = (event) => {
                    let errorMsg = `Ошибка: ${event.error}`;

                    switch(event.error) {
                        case 'no-speech':
                            errorMsg += '\nРечь не обнаружена. Говорите громче или ближе к микрофону.';
                            break;
                        case 'audio-capture':
                            errorMsg += '\nПроблема с захватом аудио. Проверьте микрофон.';
                            break;
                        case 'not-allowed':
                            errorMsg += '\nДоступ к микрофону запрещен. Разрешите доступ в настройках браузера.';
                            break;
                        case 'network':
                            errorMsg += '\nПроблема с сетью. Проверьте интернет соединение.';
                            break;
                        case 'language-not-supported':
                            errorMsg += '\nЯзык не поддерживается. Попробуем английский...';
                            // Переключаемся на английский
                            setTimeout(() => {
                                recognition.lang = 'en-US';
                                recognition.start();
                            }, 1000);
                            return;
                    }

                    showResult('speechResult', errorMsg, 'error');
                };

                recognition.onend = () => {
                    if (!hasResults) {
                        showResult('speechResult',
                            '❌ Речь не распознана.\n\nВозможные причины:\n' +
                            '• Микрофон не работает\n' +
                            '• Говорили слишком тихо\n' +
                            '• Проблемы с интернетом\n' +
                            '• Язык не поддерживается\n\n' +
                            'Попробуйте:\n' +
                            '• Говорить громче и четче\n' +
                            '• Проверить настройки микрофона\n' +
                            '• Использовать Chrome (лучшая поддержка)',
                            'error');
                    }
                };

                recognition.start();

                // Останавливаем через 10 секунд
                setTimeout(() => {
                    recognition.stop();
                }, 10000);

            } catch (error) {
                showResult('speechResult', `Критическая ошибка: ${error.message}`, 'error');
            }
        }

        async function testAudioRecording() {
            const btn = document.getElementById('recordBtn');
            btn.disabled = true;
            btn.textContent = 'Запись аудио...';

            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 48000,
                        channelCount: 2
                    }
                });

                let mimeType = 'audio/webm;codecs=opus';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'audio/webm';
                }

                const mediaRecorder = new MediaRecorder(stream, {
                    mimeType: mimeType,
                    audioBitsPerSecond: 128000
                });
                
                const chunks = [];
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        chunks.push(event.data);
                    }
                };
                
                mediaRecorder.onstop = () => {
                    const blob = new Blob(chunks, { type: 'audio/webm' });
                    const url = URL.createObjectURL(blob);
                    
                    showResult('recordingResult', 
                        `Тест записи успешен!\nРазмер: ${(blob.size / 1024).toFixed(1)} KB\nФормат: ${blob.type}`, 
                        'success');
                    
                    // Создаем ссылку для скачивания
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'test-recording.webm';
                    a.textContent = 'Скачать тестовую запись';
                    a.style.display = 'block';
                    a.style.marginTop = '10px';
                    
                    document.getElementById('recordingResult').appendChild(a);
                    
                    stream.getTracks().forEach(track => track.stop());
                };
                
                mediaRecorder.start();
                
                // Останавливаем через 5 секунд
                setTimeout(() => {
                    mediaRecorder.stop();
                    btn.disabled = false;
                    btn.textContent = 'Начать тест записи';
                }, 5000);
                
            } catch (error) {
                showResult('recordingResult', `Ошибка записи: ${error.message}`, 'error');
                btn.disabled = false;
                btn.textContent = 'Начать тест записи';
            }
        }
    </script>
</body>
</html>
