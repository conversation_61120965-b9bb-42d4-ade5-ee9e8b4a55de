class MeetRecorderPopup {
    constructor() {
        this.isRecording = false;
        this.recordingStartTime = null;
        this.timerInterval = null;
        
        this.initElements();
        this.initEventListeners();
        this.loadRecordings();
        this.checkMeetStatus();
        this.updateUI();
    }
    
    initElements() {
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusMessage = document.getElementById('status-message');
        this.recordingTime = document.getElementById('recording-time');
        this.errorMessage = document.getElementById('error-message');
        this.recordingsList = document.getElementById('recordings-list');
        this.noRecordings = document.getElementById('no-recordings');
        this.clearHistoryBtn = document.getElementById('clear-history');
        this.currentRecording = document.getElementById('current-recording');
        this.currentTime = document.getElementById('current-time');
        this.currentStatus = document.getElementById('current-status');
        this.recordings = [];
    }
    
    initEventListeners() {
        // Кнопка очистки истории
        if (this.clearHistoryBtn) {
            this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
        }

        // Слушаем сообщения от background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message);
        });
    }
    
    async checkMeetStatus() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('meet.google.com')) {
                this.showError('Откройте страницу Google Meet для начала записи');
                this.startBtn.disabled = true;
                return;
            }

            // Проверяем статус записи из background script
            const backgroundResponse = await chrome.runtime.sendMessage({ action: 'getRecordingState' });
            if (backgroundResponse && backgroundResponse.isRecording) {
                this.isRecording = true;
                this.recordingStartTime = backgroundResponse.startTime;
                this.startTimer();
                this.updateUI();
                return;
            }

            // Также проверяем статус в content script
            try {
                const response = await chrome.tabs.sendMessage(tab.id, { action: 'getRecordingStatus' });
                if (response) {
                    this.isRecording = response.isRecording;
                    if (this.isRecording) {
                        this.recordingStartTime = response.startTime;
                        this.startTimer();
                    }
                }
            } catch (contentError) {
                console.warn('Content script not ready:', contentError);
            }

        } catch (error) {
            console.error('Error checking meet status:', error);
            this.showError('Не удается подключиться к странице Meet');
        }
    }
    
    async loadRecordings() {
        try {
            const result = await chrome.storage.local.get('recordings');
            this.recordings = result.recordings || [];
            this.renderRecordings();
        } catch (error) {
            console.error('Ошибка загрузки записей:', error);
        }
    }

    renderRecordings() {
        if (!this.recordingsList) return;

        // Очищаем список
        this.recordingsList.innerHTML = '';

        if (this.recordings.length === 0) {
            this.recordingsList.appendChild(this.noRecordings);
            return;
        }

        // Сортируем по дате (новые сверху)
        const sortedRecordings = [...this.recordings].sort((a, b) =>
            new Date(b.timestamp) - new Date(a.timestamp)
        );

        sortedRecordings.forEach((recording, index) => {
            const recordingElement = this.createRecordingElement(recording, index);
            this.recordingsList.appendChild(recordingElement);
        });
    }

    createRecordingElement(recording, index) {
        const element = document.createElement('div');
        element.className = 'recording-item';
        element.dataset.index = index;

        const date = new Date(recording.timestamp);
        const timeString = this.formatTime(recording.duration);
        const dateString = date.toLocaleDateString('ru-RU');
        const timeOfDay = date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });

        element.innerHTML = `
            <div class="recording-header">
                <span class="recording-title">📝 Запись ${index + 1}</span>
                <span class="recording-time">${timeString}</span>
            </div>
            <div class="recording-status">${dateString} в ${timeOfDay}</div>
            <div class="recording-transcript">
                <div class="transcript-text">${recording.transcript || 'Транскрипция недоступна'}</div>
                <div class="transcript-meta">
                    <span>Слов: ${recording.wordCount || 0}</span>
                    <span>Размер: ${this.formatFileSize(recording.fileSize)}</span>
                </div>
            </div>
        `;

        // Обработчик клика для раскрытия/скрытия транскрипции
        const header = element.querySelector('.recording-header');
        const transcript = element.querySelector('.recording-transcript');

        header.addEventListener('click', () => {
            const isExpanded = transcript.classList.contains('expanded');

            // Закрываем все остальные
            document.querySelectorAll('.recording-transcript.expanded').forEach(t => {
                t.classList.remove('expanded');
            });

            // Переключаем текущий
            if (!isExpanded) {
                transcript.classList.add('expanded');
            }
        });

        return element;
    }

    async clearHistory() {
        if (confirm('Удалить всю историю записей?')) {
            try {
                await chrome.storage.local.remove('recordings');
                this.recordings = [];
                this.renderRecordings();
            } catch (error) {
                console.error('Ошибка очистки истории:', error);
            }
        }
    }



    startTimer() {
        // Останавливаем предыдущий таймер если есть
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }

        console.log('Запуск таймера, startTime:', this.recordingStartTime);

        this.timerInterval = setInterval(() => {
            if (this.recordingStartTime) {
                const elapsed = Date.now() - this.recordingStartTime;
                const timeString = this.formatTime(elapsed);
                console.log('Обновление таймера:', timeString);

                if (this.recordingTime) {
                    this.recordingTime.textContent = timeString;
                } else {
                    console.error('Элемент recording-time не найден');
                }
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    updateUI() {
        if (this.isRecording) {
            this.statusIndicator.className = 'status-indicator recording';
            this.statusMessage.textContent = 'Идет запись аудио + транскрибация...';
            if (this.recordingTime) this.recordingTime.style.display = 'block';
            if (this.currentRecording) this.currentRecording.style.display = 'block';
        } else {
            this.statusIndicator.className = 'status-indicator ready';
            this.statusMessage.textContent = 'Готов к записи';
            if (this.recordingTime) this.recordingTime.style.display = 'none';
            if (this.currentRecording) this.currentRecording.style.display = 'none';
        }
    }

    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    
    handleMessage(message) {
        switch (message.action) {
            case 'recordingStarted':
                this.isRecording = true;
                this.recordingStartTime = message.startTime;
                this.startTimer();
                this.updateUI();
                if (this.currentStatus) {
                    this.currentStatus.textContent = 'Запись в процессе...';
                }
                break;

            case 'recordingStopped':
                this.isRecording = false;
                this.recordingStartTime = null;
                this.stopTimer();
                this.updateUI();
                // Перезагружаем записи для отображения новой
                this.loadRecordings();
                break;

            case 'recordingError':
                this.showError(message.error);
                break;

            case 'transcriptUpdate':
                if (this.currentStatus) {
                    this.currentStatus.textContent = message.interim || 'Обработка аудио...';
                }
                break;

            case 'transcriptSaved':
                // Добавляем новую запись в историю
                this.addNewRecording(message);
                break;
        }
    }

    async addNewRecording(transcriptInfo) {
        const newRecording = {
            id: Date.now().toString(),
            timestamp: new Date().toISOString(),
            filename: transcriptInfo.filename,
            hasContent: transcriptInfo.hasContent,
            wordCount: transcriptInfo.wordCount,
            transcript: transcriptInfo.transcript || '',
            duration: this.recordingStartTime ? Date.now() - this.recordingStartTime : 0,
            fileSize: 0 // Будет обновлено позже если доступно
        };

        this.recordings.unshift(newRecording); // Добавляем в начало

        // Ограничиваем количество записей (последние 20)
        if (this.recordings.length > 20) {
            this.recordings = this.recordings.slice(0, 20);
        }

        // Сохраняем в storage
        try {
            await chrome.storage.local.set({ recordings: this.recordings });
            this.renderRecordings();
        } catch (error) {
            console.error('Ошибка сохранения записи:', error);
        }
    }


    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
    }
    
    hideError() {
        this.errorMessage.style.display = 'none';
    }
}

// Инициализируем popup когда DOM загружен
document.addEventListener('DOMContentLoaded', () => {
    new MeetRecorderPopup();
});
