class MeetRecorderPopup {
    constructor() {
        this.isRecording = false;
        this.recordingStartTime = null;
        this.timerInterval = null;
        
        this.initElements();
        this.initEventListeners();
        this.checkMeetStatus();
        this.updateUI();
    }
    
    initElements() {
        this.startBtn = document.getElementById('start-btn');
        this.stopBtn = document.getElementById('stop-btn');
        this.forceStopBtn = document.getElementById('force-stop-btn');
        this.forceControls = document.getElementById('force-controls');
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusMessage = document.getElementById('status-message');
        this.recordingTime = document.getElementById('recording-time');
        this.errorMessage = document.getElementById('error-message');
        this.transcriptSection = document.getElementById('transcript-section');
        this.transcriptText = document.getElementById('transcript-text');
        this.interimText = document.getElementById('interim-text');
    }
    
    initEventListeners() {
        this.startBtn.addEventListener('click', () => this.startRecording());
        this.stopBtn.addEventListener('click', () => this.stopRecording());
        this.forceStopBtn.addEventListener('click', () => this.forceStopRecording());

        // Слушаем сообщения от background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message);
        });
    }
    
    async checkMeetStatus() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('meet.google.com')) {
                this.showError('Откройте страницу Google Meet для начала записи');
                this.startBtn.disabled = true;
                return;
            }

            // Проверяем статус записи из background script
            const backgroundResponse = await chrome.runtime.sendMessage({ action: 'getRecordingState' });
            if (backgroundResponse && backgroundResponse.isRecording) {
                this.isRecording = true;
                this.recordingStartTime = backgroundResponse.startTime;
                this.startTimer();
                this.updateUI();
                return;
            }

            // Также проверяем статус в content script
            try {
                const response = await chrome.tabs.sendMessage(tab.id, { action: 'getRecordingStatus' });
                if (response) {
                    this.isRecording = response.isRecording;
                    if (this.isRecording) {
                        this.recordingStartTime = response.startTime;
                        this.startTimer();
                    }
                }
            } catch (contentError) {
                console.warn('Content script not ready:', contentError);
            }

        } catch (error) {
            console.error('Error checking meet status:', error);
            this.showError('Не удается подключиться к странице Meet');
        }
    }
    
    async startRecording() {
        try {
            this.hideError();
            
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Отправляем команду на начало записи
            const response = await chrome.tabs.sendMessage(tab.id, { action: 'startRecording' });
            
            if (response && response.success) {
                this.isRecording = true;
                this.recordingStartTime = Date.now();
                this.startTimer();
                this.updateUI();
            } else {
                this.showError(response?.error || 'Не удалось начать запись');
            }
        } catch (error) {
            console.error('Error starting recording:', error);
            this.showError('Ошибка при запуске записи');
        }
    }
    
    async stopRecording() {
        try {
            this.hideError();

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // Пытаемся остановить через content script
            let stopped = false;
            try {
                const response = await chrome.tabs.sendMessage(tab.id, { action: 'stopRecording' });
                if (response && response.success) {
                    stopped = true;
                }
            } catch (contentError) {
                console.warn('Content script stop failed:', contentError);
            }

            // Если content script не ответил, принудительно останавливаем через background
            if (!stopped) {
                console.log('Принудительная остановка через background script');
                await chrome.runtime.sendMessage({ action: 'forceStopRecording' });
            }

            // Обновляем UI независимо от результата
            this.isRecording = false;
            this.recordingStartTime = null;
            this.stopTimer();
            this.updateUI();

        } catch (error) {
            console.error('Error stopping recording:', error);

            // Даже при ошибке сбрасываем состояние UI
            this.isRecording = false;
            this.recordingStartTime = null;
            this.stopTimer();
            this.updateUI();

            this.showError('Запись остановлена (возможны ошибки при сохранении)');
        }
    }

    async forceStopRecording() {
        try {
            console.log('Принудительная остановка записи');

            // Отправляем команду принудительной остановки в background script
            await chrome.runtime.sendMessage({ action: 'forceStopRecording' });

            // Сбрасываем состояние UI
            this.isRecording = false;
            this.recordingStartTime = null;
            this.stopTimer();
            this.updateUI();

            this.showError('Запись принудительно остановлена');

        } catch (error) {
            console.error('Error force stopping recording:', error);

            // Все равно сбрасываем UI
            this.isRecording = false;
            this.recordingStartTime = null;
            this.stopTimer();
            this.updateUI();
        }
    }

    startTimer() {
        // Останавливаем предыдущий таймер если есть
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }

        console.log('Запуск таймера, startTime:', this.recordingStartTime);

        this.timerInterval = setInterval(() => {
            if (this.recordingStartTime) {
                const elapsed = Date.now() - this.recordingStartTime;
                const timeString = this.formatTime(elapsed);
                console.log('Обновление таймера:', timeString);

                if (this.recordingTime) {
                    this.recordingTime.textContent = timeString;
                } else {
                    console.error('Элемент recording-time не найден');
                }
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    updateUI() {
        if (this.isRecording) {
            this.statusIndicator.className = 'status-indicator recording';
            this.statusMessage.textContent = 'Идет запись аудио + транскрибация...';
            this.recordingTime.style.display = 'block';
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
            this.forceControls.style.display = 'block';
            this.transcriptSection.style.display = 'block';
        } else {
            this.statusIndicator.className = 'status-indicator ready';
            this.statusMessage.textContent = 'Готов к записи';
            this.recordingTime.style.display = 'none';
            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
            this.forceControls.style.display = 'none';
            this.transcriptSection.style.display = 'none';

            // Очищаем транскрипт
            this.transcriptText.textContent = '';
            this.interimText.textContent = '';
        }
    }
    
    handleMessage(message) {
        switch (message.action) {
            case 'recordingStarted':
                this.isRecording = true;
                this.recordingStartTime = message.startTime;
                this.startTimer();
                this.updateUI();
                break;

            case 'recordingStopped':
                this.isRecording = false;
                this.recordingStartTime = null;
                this.stopTimer();
                this.updateUI();
                break;

            case 'recordingError':
                this.showError(message.error);
                break;

            case 'transcriptUpdate':
                this.updateTranscript(message.transcript, message.interim);
                break;
        }
    }

    updateTranscript(transcript, interim) {
        if (transcript) {
            this.transcriptText.textContent = transcript;
            // Прокручиваем вниз
            this.transcriptText.scrollTop = this.transcriptText.scrollHeight;
        }

        if (interim !== undefined) {
            if (interim.includes('Ожидание речи') || interim.includes('Слушаю')) {
                this.interimText.textContent = interim;
                this.interimText.style.color = '#aaa';
            } else if (interim.includes('Ошибка')) {
                this.interimText.textContent = interim;
                this.interimText.style.color = '#ff6b6b';
            } else if (interim.includes('Переключение')) {
                this.interimText.textContent = interim;
                this.interimText.style.color = '#4CAF50';
            } else if (interim) {
                this.interimText.textContent = `Распознается: ${interim}`;
                this.interimText.style.color = '#667eea';
            } else {
                this.interimText.textContent = '';
            }
        }
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
    }
    
    hideError() {
        this.errorMessage.style.display = 'none';
    }
}

// Инициализируем popup когда DOM загружен
document.addEventListener('DOMContentLoaded', () => {
    new MeetRecorderPopup();
});
