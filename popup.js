class MeetRecorderPopup {
    constructor() {
        this.isRecording = false;
        this.recordingStartTime = null;
        this.timerInterval = null;
        
        this.initElements();
        this.initEventListeners();
        this.checkMeetStatus();
        this.updateUI();
    }
    
    initElements() {
        this.startBtn = document.getElementById('start-btn');
        this.stopBtn = document.getElementById('stop-btn');
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusMessage = document.getElementById('status-message');
        this.recordingTime = document.getElementById('recording-time');
        this.errorMessage = document.getElementById('error-message');
    }
    
    initEventListeners() {
        this.startBtn.addEventListener('click', () => this.startRecording());
        this.stopBtn.addEventListener('click', () => this.stopRecording());
        
        // Слушаем сообщения от background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message);
        });
    }
    
    async checkMeetStatus() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('meet.google.com')) {
                this.showError('Откройте страницу Google Meet для начала записи');
                this.startBtn.disabled = true;
                return;
            }
            
            // Проверяем статус записи
            const response = await chrome.tabs.sendMessage(tab.id, { action: 'getRecordingStatus' });
            if (response) {
                this.isRecording = response.isRecording;
                if (this.isRecording) {
                    this.recordingStartTime = response.startTime;
                    this.startTimer();
                }
            }
        } catch (error) {
            console.error('Error checking meet status:', error);
            this.showError('Не удается подключиться к странице Meet');
        }
    }
    
    async startRecording() {
        try {
            this.hideError();
            
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Отправляем команду на начало записи
            const response = await chrome.tabs.sendMessage(tab.id, { action: 'startRecording' });
            
            if (response && response.success) {
                this.isRecording = true;
                this.recordingStartTime = Date.now();
                this.startTimer();
                this.updateUI();
            } else {
                this.showError(response?.error || 'Не удалось начать запись');
            }
        } catch (error) {
            console.error('Error starting recording:', error);
            this.showError('Ошибка при запуске записи');
        }
    }
    
    async stopRecording() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.tabs.sendMessage(tab.id, { action: 'stopRecording' });
            
            if (response && response.success) {
                this.isRecording = false;
                this.recordingStartTime = null;
                this.stopTimer();
                this.updateUI();
            } else {
                this.showError(response?.error || 'Не удалось остановить запись');
            }
        } catch (error) {
            console.error('Error stopping recording:', error);
            this.showError('Ошибка при остановке записи');
        }
    }
    
    startTimer() {
        this.timerInterval = setInterval(() => {
            if (this.recordingStartTime) {
                const elapsed = Date.now() - this.recordingStartTime;
                this.recordingTime.textContent = this.formatTime(elapsed);
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    updateUI() {
        if (this.isRecording) {
            this.statusIndicator.className = 'status-indicator recording';
            this.statusMessage.textContent = 'Идет запись...';
            this.recordingTime.style.display = 'block';
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
        } else {
            this.statusIndicator.className = 'status-indicator ready';
            this.statusMessage.textContent = 'Готов к записи';
            this.recordingTime.style.display = 'none';
            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
        }
    }
    
    handleMessage(message) {
        switch (message.action) {
            case 'recordingStarted':
                this.isRecording = true;
                this.recordingStartTime = message.startTime;
                this.startTimer();
                this.updateUI();
                break;
                
            case 'recordingStopped':
                this.isRecording = false;
                this.recordingStartTime = null;
                this.stopTimer();
                this.updateUI();
                break;
                
            case 'recordingError':
                this.showError(message.error);
                break;
        }
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
    }
    
    hideError() {
        this.errorMessage.style.display = 'none';
    }
}

// Инициализируем popup когда DOM загружен
document.addEventListener('DOMContentLoaded', () => {
    new MeetRecorderPopup();
});
