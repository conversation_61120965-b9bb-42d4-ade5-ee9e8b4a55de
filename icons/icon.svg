<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- Camera body -->
  <rect x="30" y="45" width="68" height="38" rx="8" fill="#fff"/>
  
  <!-- Camera lens -->
  <circle cx="55" cy="64" r="12" fill="#333"/>
  <circle cx="55" cy="64" r="8" fill="#667eea"/>
  <circle cx="55" cy="64" r="4" fill="#fff"/>
  
  <!-- Recording indicator -->
  <circle cx="85" cy="55" r="6" fill="#ff4757"/>
  
  <!-- Microphone -->
  <rect x="75" y="70" width="8" height="12" rx="4" fill="#333"/>
  <path d="M71 76 Q71 82 79 82 Q87 82 87 76" stroke="#333" stroke-width="2" fill="none"/>
  <line x1="79" y1="82" x2="79" y2="88" stroke="#333" stroke-width="2"/>
  <line x1="75" y1="88" x2="83" y2="88" stroke="#333" stroke-width="2"/>
</svg>
