// Injected script для глубокой интеграции с Google Meet
(function() {
    'use strict';
    
    class MeetIntegration {
        constructor() {
            this.originalGetUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
            this.meetAudioTracks = new Set();
            this.meetVideoTracks = new Set();
            
            this.interceptMediaDevices();
            this.monitorMeetState();
        }
        
        interceptMediaDevices() {
            // Перехватываем вызовы getUserMedia для отслеживания аудио потоков Meet
            navigator.mediaDevices.getUserMedia = async (constraints) => {
                try {
                    const stream = await this.originalGetUserMedia(constraints);
                    
                    // Отслеживаем аудио треки от Meet
                    if (constraints.audio) {
                        stream.getAudioTracks().forEach(track => {
                            this.meetAudioTracks.add(track);
                            
                            // Слушаем окончание трека
                            track.addEventListener('ended', () => {
                                this.meetAudioTracks.delete(track);
                            });
                        });
                    }
                    
                    // Отслеживаем видео треки от Meet
                    if (constraints.video) {
                        stream.getVideoTracks().forEach(track => {
                            this.meetVideoTracks.add(track);
                            
                            track.addEventListener('ended', () => {
                                this.meetVideoTracks.delete(track);
                            });
                        });
                    }
                    
                    return stream;
                } catch (error) {
                    throw error;
                }
            };
        }
        
        monitorMeetState() {
            // Отслеживаем состояние встречи
            let lastMeetState = this.getMeetState();
            
            setInterval(() => {
                const currentState = this.getMeetState();
                
                if (JSON.stringify(currentState) !== JSON.stringify(lastMeetState)) {
                    this.notifyStateChange(currentState, lastMeetState);
                    lastMeetState = currentState;
                }
            }, 1000);
        }
        
        getMeetState() {
            const state = {
                inMeeting: false,
                micMuted: false,
                cameraOff: false,
                participantCount: 0,
                meetingTitle: '',
                isPresenting: false
            };
            
            // Проверяем, находимся ли мы во встрече
            const meetingContainer = document.querySelector('[data-meeting-title]') || 
                                   document.querySelector('[jsname="HlFzId"]') ||
                                   document.querySelector('.crqnQb');
            
            state.inMeeting = !!meetingContainer;
            
            if (state.inMeeting) {
                // Получаем название встречи
                const titleElement = document.querySelector('[data-meeting-title]') ||
                                   document.querySelector('.u6vdEc') ||
                                   document.querySelector('.tL9Q4c');
                
                if (titleElement) {
                    state.meetingTitle = titleElement.textContent || titleElement.innerText || '';
                }
                
                // Проверяем состояние микрофона
                const micButton = document.querySelector('[data-is-muted="true"]') ||
                                document.querySelector('[aria-label*="микрофон"][aria-pressed="true"]') ||
                                document.querySelector('.U26fgb[aria-label*="Включить микрофон"]');
                
                state.micMuted = !!micButton;
                
                // Проверяем состояние камеры
                const cameraButton = document.querySelector('[aria-label*="камер"][aria-pressed="true"]') ||
                                   document.querySelector('.U26fgb[aria-label*="Включить камеру"]');
                
                state.cameraOff = !!cameraButton;
                
                // Подсчитываем участников
                const participantElements = document.querySelectorAll('[data-participant-id]') ||
                                          document.querySelectorAll('.KV1GEc') ||
                                          document.querySelectorAll('.MdGzEb');
                
                state.participantCount = participantElements.length;
                
                // Проверяем, идет ли демонстрация экрана
                const presentingIndicator = document.querySelector('[aria-label*="демонстрац"]') ||
                                          document.querySelector('[aria-label*="Presenting"]') ||
                                          document.querySelector('.Jyj1Td');
                
                state.isPresenting = !!presentingIndicator;
            }
            
            return state;
        }
        
        notifyStateChange(newState, oldState) {
            // Отправляем уведомление о изменении состояния
            window.postMessage({
                type: 'MEET_STATE_CHANGE',
                newState: newState,
                oldState: oldState
            }, '*');
            
            // Логируем важные изменения
            if (newState.inMeeting !== oldState.inMeeting) {
                console.log(newState.inMeeting ? 'Вошли во встречу' : 'Покинули встречу');
            }
            
            if (newState.participantCount !== oldState.participantCount) {
                console.log(`Количество участников изменилось: ${newState.participantCount}`);
            }
        }
        
        // Метод для получения всех активных аудио треков Meet
        getMeetAudioTracks() {
            return Array.from(this.meetAudioTracks).filter(track => track.readyState === 'live');
        }
        
        // Метод для получения всех активных видео треков Meet
        getMeetVideoTracks() {
            return Array.from(this.meetVideoTracks).filter(track => track.readyState === 'live');
        }
        
        // Метод для улучшения качества записи аудио
        enhanceAudioCapture() {
            // Настройки для лучшего качества аудио
            const audioConstraints = {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                sampleRate: 48000,
                channelCount: 2,
                sampleSize: 16
            };
            
            return audioConstraints;
        }
    }
    
    // Инициализируем интеграцию
    const meetIntegration = new MeetIntegration();
    
    // Делаем доступным для content script
    window.meetIntegration = meetIntegration;
    
    // Слушаем сообщения от content script
    window.addEventListener('message', (event) => {
        if (event.source !== window) return;
        
        switch (event.data.type) {
            case 'GET_MEET_STATE':
                window.postMessage({
                    type: 'MEET_STATE_RESPONSE',
                    state: meetIntegration.getMeetState()
                }, '*');
                break;
                
            case 'GET_AUDIO_TRACKS':
                window.postMessage({
                    type: 'AUDIO_TRACKS_RESPONSE',
                    tracks: meetIntegration.getMeetAudioTracks().map(track => ({
                        id: track.id,
                        label: track.label,
                        kind: track.kind,
                        enabled: track.enabled
                    }))
                }, '*');
                break;
        }
    });
    
    console.log('Google Meet Recorder: Injected script loaded');
})();
